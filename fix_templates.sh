#!/bin/bash

# Define paths
TEMPLATES_DIR="/usr/local/agfunder/agbase_admin/gaia/djangaia/gaia_chat/templates/gaia_chat"
SOURCE_DIR="gaia/djangaia/gaia_chat/templates/gaia_chat"

# Create templates directory if it doesn't exist
echo "Creating templates directory if it doesn't exist..."
mkdir -p $TEMPLATES_DIR

# List all template files
echo "Checking template files..."
if [ -d "$SOURCE_DIR" ]; then
    echo "Source directory exists. Template files:"
    ls -la $SOURCE_DIR
else
    echo "Source directory does not exist!"
fi

echo "Checking destination directory..."
if [ -d "$TEMPLATES_DIR" ]; then
    echo "Destination directory exists. Template files:"
    ls -la $TEMPLATES_DIR
else
    echo "Destination directory does not exist!"
fi

# Instructions for manual copying
echo ""
echo "To fix the template issue, you need to copy all template files to the production server."
echo "You can use the following commands on your local machine:"
echo ""
echo "# Create the templates directory on the production server"
echo "ssh user@production-server 'mkdir -p $TEMPLATES_DIR'"
echo ""
echo "# Copy the template files to the production server"
echo "scp $SOURCE_DIR/* user@production-server:$TEMPLATES_DIR/"
echo ""
echo "Or, if you have direct access to the production server, copy the files manually."
