"""
API views for the gaia_chat app.

These views provide a REST API for interacting with the chatobj functionality.
"""

import json
import os
import logging
from datetime import datetime
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods

# Import chatobj components
from gaia.gaia_ceto.ceto_v002.chatobj import (
    ChatManager, Conversation, MockLLM, OpenAILLM, AnthropicLLM
)

# Set up logging
logger = logging.getLogger(__name__)

# Global chat manager instance
STORAGE_DIR = "/var/lib/gaia/GAIA_FS/ceto_conversations"
os.makedirs(STORAGE_DIR, exist_ok=True)
chat_manager = ChatManager(storage_dir=STORAGE_DIR)

# LLM factory function
def create_llm(llm_type="mock", model_name=None):
    """Create an LLM instance based on the specified type.
    
    Args:
        llm_type: The type of LLM to create ("mock", "openai", or "anthropic").
        model_name: The specific model name to use (if applicable).
        
    Returns:
        An instance of the specified LLM.
    """
    kwargs = {}
    if model_name:
        kwargs["model_name"] = model_name
        
    if llm_type.lower() == "mock":
        return MockLLM()
    elif llm_type.lower() == "openai":
        try:
            return OpenAILLM(**kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI LLM: {e}")
            return MockLLM()
    elif llm_type.lower() == "anthropic":
        try:
            return AnthropicLLM(**kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize Anthropic LLM: {e}")
            return MockLLM()
    else:
        logger.warning(f"Unsupported LLM type: {llm_type}. Using MockLLM instead.")
        return MockLLM()

# API endpoints
@require_http_methods(["GET"])
def get_llm_providers(request):
    """Get available LLM providers."""
    providers = [
        {"id": "mock", "name": "Mock LLM"},
        {"id": "openai", "name": "OpenAI"},
        {"id": "anthropic", "name": "Anthropic"}
    ]
    return JsonResponse({"providers": providers})

@require_http_methods(["GET"])
def get_models(request):
    """Get available models for a specific LLM provider."""
    provider = request.GET.get("provider", "mock")
    
    if provider == "mock":
        models = [{"id": "default", "name": "Default Mock"}]
    elif provider == "openai":
        try:
            models = [{"id": model, "name": model} for model in OpenAILLM.get_available_models()]
        except Exception as e:
            logger.error(f"Error getting OpenAI models: {e}")
            models = [
                {"id": "gpt-3.5-turbo", "name": "GPT-3.5 Turbo"},
                {"id": "gpt-4", "name": "GPT-4"}
            ]
    elif provider == "anthropic":
        try:
            models = [{"id": model, "name": model} for model in AnthropicLLM.get_available_models()]
        except Exception as e:
            logger.error(f"Error getting Anthropic models: {e}")
            models = [
                {"id": "claude-3-sonnet-20240229", "name": "Claude 3 Sonnet"},
                {"id": "claude-3-opus-20240229", "name": "Claude 3 Opus"}
            ]
    else:
        models = []
        
    return JsonResponse({"models": models})

@require_http_methods(["POST"])
@csrf_exempt
def set_llm(request):
    """Set the LLM for the chat manager."""
    try:
        data = json.loads(request.body)
        provider = data.get("provider", "mock")
        model = data.get("model")
        
        # Create the LLM
        llm = create_llm(provider, model)
        
        # Update the chat manager
        global chat_manager
        chat_manager.llm = llm
        
        return JsonResponse({
            "success": True,
            "provider": provider,
            "model": model
        })
    except Exception as e:
        logger.error(f"Error setting LLM: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=400)

@require_http_methods(["GET"])
def list_conversations(request):
    """List available conversations."""
    try:
        conversations = chat_manager.list_conversations()
        
        # Format the conversations for the frontend
        formatted = []
        for conv in conversations:
            formatted.append({
                "id": conv["conversation_id"],
                "title": conv["title"],
                "created_at": conv["created_at"],
                "message_count": conv["message_count"],
                "path": conv["relative_path"]
            })
            
        return JsonResponse({"conversations": formatted})
    except Exception as e:
        logger.error(f"Error listing conversations: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@require_http_methods(["POST"])
@csrf_exempt
def create_conversation(request):
    """Create a new conversation."""
    try:
        data = json.loads(request.body)
        title = data.get("title", f"Conversation {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        user_id = data.get("user_id", request.user.username if request.user.is_authenticated else "anonymous")
        
        # Create the conversation
        conversation = chat_manager.create_conversation(
            title=title,
            user_id=user_id
        )
        
        # Add a system message
        chat_manager.add_message("system", "Welcome to Gaia Chat! How can I help you today?")
        
        # Save the conversation
        chat_manager.save_conversation()
        
        return JsonResponse({
            "success": True,
            "conversation": {
                "id": conversation.conversation_id,
                "title": conversation.title,
                "created_at": conversation.created_at
            }
        })
    except Exception as e:
        logger.error(f"Error creating conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@require_http_methods(["GET"])
def load_conversation(request, conversation_id):
    """Load a conversation by ID."""
    try:
        conversation = chat_manager.load_conversation(conversation_id)
        
        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)
            
        # Set as active conversation
        chat_manager.active_conversation = conversation
        
        # Format the messages
        messages = []
        for msg in conversation.messages:
            messages.append({
                "role": msg["role"],
                "content": msg["content"],
                "timestamp": msg.get("timestamp", "")
            })
            
        return JsonResponse({
            "success": True,
            "conversation": {
                "id": conversation.conversation_id,
                "title": conversation.title,
                "created_at": conversation.created_at,
                "messages": messages
            }
        })
    except Exception as e:
        logger.error(f"Error loading conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@require_http_methods(["POST"])
@csrf_exempt
def send_message(request):
    """Send a message to the active conversation."""
    try:
        data = json.loads(request.body)
        message = data.get("message", "")
        
        if not message.strip():
            return JsonResponse({
                "success": False,
                "error": "Message cannot be empty"
            }, status=400)
            
        # Process the message
        response = chat_manager.process_message(message)
        
        # Save the conversation
        chat_manager.save_conversation()
        
        return JsonResponse({
            "success": True,
            "response": response
        })
    except ValueError as e:
        # This is likely due to no active conversation
        logger.error(f"Error sending message: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e),
            "code": "no_active_conversation"
        }, status=400)
    except Exception as e:
        logger.error(f"Error sending message: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)
