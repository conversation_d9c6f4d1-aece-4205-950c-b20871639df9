from django.urls import path
from . import views
from . import api

app_name = 'gaia_chat'

urlpatterns = [
    # Main Vue.js app view
    path('', views.chat_app, name='chat_app'),

    # API endpoints
    path('api/llm/providers/', api.get_llm_providers, name='api_llm_providers'),
    path('api/llm/models/', api.get_models, name='api_llm_models'),
    path('api/llm/set/', api.set_llm, name='api_set_llm'),
    path('api/conversations/', api.list_conversations, name='api_list_conversations'),
    path('api/conversations/create/', api.create_conversation, name='api_create_conversation'),
    path('api/conversations/<str:conversation_id>/', api.load_conversation, name='api_load_conversation'),
    path('api/messages/send/', api.send_message, name='api_send_message'),
]
