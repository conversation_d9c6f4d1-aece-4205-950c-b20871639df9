from django.shortcuts import render
from django.http import HttpResponse
import os


def chat_app(request):
    """
    Renders the Vue.js chat application.
    """
    # Check if API keys are available
    openai_api_key = os.environ.get("OPENAI_API_KEY", "")
    anthropic_api_key = os.environ.get("ANTHROPIC_API_KEY", "")

    context = {
        'title': 'Gaia Chat',
        'has_openai_key': bool(openai_api_key),
        'has_anthropic_key': bool(anthropic_api_key),
    }
    return render(request, 'gaia_chat/chat_app.html', context)
