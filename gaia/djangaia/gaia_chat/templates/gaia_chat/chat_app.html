<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    {% csrf_token %}
    <!-- Vue.js -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <!-- Axios for HTTP requests -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- Marked for Markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- CSS -->
    <style>
        :root {
            --primary-color: #f4f5ef;
            --secondary-color: #ccc;
            --background-color: #f4f4f4;
            --text-color: #333;
            --light-text: #666;
            --border-color: #ddd;
            --success-color: #4caf50;
            --error-color: #f44336;
            --warning-color: #ff9800;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-family: Verdana, sans-serif;
            line-height: 1.6;
            background-color: var(--background-color);
            color: var(--text-color);
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 300px;
            background-color: var(--primary-color);
            color: #1d1d1d;
            padding: 1rem;
            overflow-y: auto;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-container {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background-color: white;
        }
        .chat-inner {
            margin: 0 auto;
            max-width: 800px;
            width: 94%;
            padding: 1rem;
            overflow-y: auto;
            background-color: white;
        }
        .chat-inner h2 {
            background: url('');
            margin-bottom: 2rem;
        }

        .input-container {
            padding: 1rem;
            background-color: #f9f9f9;
            border-top: 1px solid var(--border-color);
        }

        .sidebar h2 {
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .sidebar h3 {
            margin: 1rem 0 0.5rem;
            font-size: 1.2rem;
            color: #1d1d1d;
        }

        .conversation-list {
            margin-top: 1rem;
        }

        .conversation-item {
            padding: 0rem 0.5rem 0rem 0.5rem;
            margin-bottom: 0.2rem;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s;
        }

        .conversation-content {
            flex: 1;
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 0.2rem 0;
        }

        .delete-button {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.5);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 1.5rem;
            width: 1.5rem;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .delete-button:hover {
            background-color: rgba(255, 0, 0, 0.2);
            color: white;
        }

        .delete-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading-small {
            display: inline-block;
            width: 0.8rem;
            height: 0.8rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        .conversation-item:hover {
            background-color: #ccc;
        }

        .conversation-item.active {
            background-color: var(--secondary-color);
        }

        .message {
            margin-bottom: 0.5rem;
            /*padding: 0.5rem 1rem;*/
            padding: 0rem 0.5rem;
            border-radius: 10px;
        }

        .message-user {
            background-color: #f0f0f0;
        }

        .message-assistant {

        }

        .message-system {
            margin: 0 1rem;
            font-style: italic;
        }

        .message-role {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .message-content {
            white-space: pre-wrap;
            padding: 0.5rem;
        }

        .input-box {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            resize: none;
            font-family: inherit;
            font-size: 1rem;
        }

        .button {
            padding: 0.5rem 1rem;
            background-color: #1d1d1d;
            color: white;
            border: none;
            /*border-radius: 4px;*/
            cursor: pointer;
            font-size: 1rem;
            margin-top: 0.5rem;
        }

        .button:hover {
            background-color: #888;
        }

        .button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .select-container {
            margin-bottom: 1rem;
        }

        .select-container label {
            display: block;
            margin-bottom: 0.25rem;
            color: #666;
        }

        .select-container select {
            width: 100%;
            padding: 0.5rem;
            border-radius: 4px;
            background-color: white;
            border: 1px solid var(--border-color);
        }

        .alert {
            padding: 0.5rem;
            margin-bottom: 1rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .alert-warning {
            background-color: #fff8e1;
            margin-top: 0.5rem;
            border-left: 4px solid var(--warning-color);
        }

        .alert-error {
            background-color: #ffebee;
            border-left: 4px solid var(--error-color);
        }

        .alert-success {
            background-color: #e8f5e9;
            border-left: 4px solid var(--success-color);
        }

        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-left: 0.5rem;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: var(--light-text);
        }

        .user-info {
            margin-bottom: 1rem;
            padding: 1rem;
            background-color: #caf22a;
            border-radius: 0px;
        }

        .user-info p {
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .user-links {
            display: flex;
            justify-content: space-between;
        }

        .user-link {
            color: var(--text-color);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .user-link:hover {
            color: var(--text-color);
            text-decoration: underline;
        }
        .logo {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div id="app" class="app-container">
        <div class="sidebar">
            <div class="logo">
                <img src="https://agfunder-gaia-assets.s3.us-west-2.amazonaws.com/agfunder_logo.svg" alt="AgFunder Logo">
              </div>
            <h2>Gaia Chat</h2>

            <div style="margin-bottom: 1.5rem">
                <img src="https://agfunder-gaia-assets.s3.us-west-2.amazonaws.com/AI-in-agriculture-istock-Igor-Borisenko.jpg" width="100%" height="100%" alt="Gaia Chat Banner" style="border-radius: 2px">
              </div>

            <!-- User Info -->
            <div class="user-info">
                <p>Welcome, {{ user.username }}</p>
                <div class="user-links">
                    <a href="{% url 'gaia_chat:profile' %}" class="user-link">Profile</a>
                    <a href="{% url 'gaia_chat:logout' %}" class="user-link">Logout</a>
                </div>
            </div>

            <!-- LLM Settings -->
             {% if user.is_superuser %}
            <h3>LLM Settings</h3>
            <div class="select-container">
                <label for="llm-provider">Provider</label>
                <select id="llm-provider" v-model="selectedProvider" @change="onProviderChange">
                    <option v-for="provider in providers" :key="provider.id" :value="provider.id">
                        [[ provider.name ]]
                    </option>
                </select>
            </div>

            <div class="select-container">
                <label for="llm-model">Model</label>
                <select id="llm-model" v-model="selectedModel" :disabled="!models.length">
                    <option v-for="model in models" :key="model.id" :value="model.id">
                        [[ model.name ]]
                    </option>
                </select>
            </div>

            <button class="button" @click="setLLM" :disabled="isSettingLLM">
               Select Model
                <!--<span v-if="isSettingLLM" class="loading"></span> -->
            </button>

            <!-- MCP Protocol Info -->
            <div v-if="selectedProvider === 'mcp' || selectedProvider === 'mcp-http'" style="margin-top: 0.5rem; font-size: 0.9rem;">
                <span v-if="selectedProvider === 'mcp'">Using MCP SSE protocol</span>
                <span v-if="selectedProvider === 'mcp-http'">Using MCP HTTP protocol</span>
            </div>

            <!-- API Key Warnings -->
            <div v-if="selectedProvider === 'openai' && !hasOpenAIKey" class="alert alert-warning">
                OpenAI API key not found. Set the OPENAI_API_KEY environment variable.
            </div>

            <div v-if="selectedProvider === 'anthropic' && !hasAnthropicKey" class="alert alert-warning">
                Anthropic API key not found. Set the ANTHROPIC_API_KEY environment variable.
            </div>
            {% endif %}

            <!-- Conversation Management -->

            <!-- New Conversation Form
            <h3>New Conversation</h3>-->
            <div style="margin-bottom: 2rem;"></div>
            <div class="new-conversation-form">
                <input
                    type="text"
                    v-model="newConversationTitle"
                    placeholder="Enter conversation title"
                    class="input-box"
                    style="margin-bottom: 0.5rem;"
                />
                <button class="button" @click="createConversation" :disabled="isCreatingConversation">
                    New Conversation
                    <span v-if="isCreatingConversation" class="loading"></span>
                </button>
            </div>

            <div style="margin-bottom: 2rem;"></div>

            <h3>Your Conversations</h3>
            <div class="conversation-list">
                <div v-if="conversations.length === 0" class="empty-state">
                    <p>You don't have any conversations yet</p>
                    <p>Create a new conversation to get started</p>
                </div>
                <div
                    v-for="conv in conversations"
                    :key="conv.id"
                    class="conversation-item"
                    :class="{ active: activeConversation && activeConversation.id === conv.id }"
                    :title="formatDate(conv.created_at)"
                >
                    <div class="conversation-content" @click="loadConversation(conv.id)">
                        [[ conv.title ]]
                    </div>
                    <button
                        class="delete-button"
                        @click.stop="deleteConversation(conv.id)"
                        :disabled="isDeletingConversation === conv.id"
                        :title="'Delete conversation'"
                    >
                        <span v-if="isDeletingConversation === conv.id" class="loading-small"></span>
                        <span v-else>&times;</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="chat-container" ref="chatContainer">
                <!-- Error display -->
                <div v-if="error" class="alert alert-error">
                    [[ error ]]
                </div>

                <div v-if="!activeConversation" class="empty-state">
                    <h3>No active conversation</h3>
                    <p>Create a new conversation or select an existing one to start chatting.</p>
                </div>

                <div v-else class="chat-inner">
                    <h2>
                        [[ activeConversation.title ]]
                        <small v-if="mcpProtocol" style="font-size: 0.7em; color: #666; font-weight: normal;">
                            (MCP [[ mcpProtocol ]])
                        </small>
                    </h2>

                    <div v-for="(message, index) in activeConversation.messages" :key="index"
                        class="message"
                        :class="{
                            'message-user': message.role === 'user',
                            'message-assistant': message.role === 'assistant',
                            'message-system': message.role === 'system'
                        }"
                    >
                        <div class="message-role">[[ formatRole(message.role) ]]</div>
                        <div class="message-content" v-html="formatMessage(message.content)"></div>
                    </div>
                </div>
            </div>

            <div class="input-container">
                <textarea
                    class="input-box"
                    v-model="userInput"
                    placeholder="Type your message here..."
                    :disabled="!activeConversation || isSendingMessage"
                    @keydown.enter.ctrl="sendMessage"
                    rows="3"
                ></textarea>
                <button
                    class="button"
                    @click="sendMessage"
                    :disabled="!activeConversation || !userInput.trim() || isSendingMessage"
                >
                    Send
                    <span v-if="isSendingMessage" class="loading"></span>
                </button>
                <small>Press Ctrl+Enter to send</small>
            </div>
        </div>
    </div>

    <script>
        // Configure Vue to use [[ ]] for interpolation to avoid conflict with Django templates
        Vue.config.delimiters = ['[[', ']]'];

        new Vue({
            el: '#app',
            delimiters: ['[[', ']]'],
            data: {
                // LLM settings
                providers: [],
                models: [],
                selectedProvider: 'mock',
                selectedModel: '',
                hasOpenAIKey: {{ has_openai_key|lower }},
                hasAnthropicKey: {{ has_anthropic_key|lower }},
                isSettingLLM: false,
                mcpProtocol: '',  // Will store the MCP protocol (SSE or HTTP)

                // Conversations
                conversations: [],
                activeConversation: null,
                newConversationTitle: '',
                isCreatingConversation: false,
                isLoadingConversation: false,
                isDeletingConversation: null,

                // Messaging
                userInput: '',
                isSendingMessage: false,

                // Errors
                error: null
            },
            mounted() {
                this.fetchProviders();
                this.fetchConversations();
            },
            updated() {
                this.scrollToBottom();
            },
            methods: {
                // API calls
                async fetchProviders() {
                    try {
                        const response = await axios.get('/gaia_chat/api/llm/providers/');
                        this.providers = response.data.providers;
                        this.onProviderChange();
                    } catch (error) {
                        console.error('Error fetching providers:', error);
                    }
                },

                async onProviderChange() {
                    try {
                        const response = await axios.get('/gaia_chat/api/llm/models/', {
                            params: { provider: this.selectedProvider }
                        });
                        this.models = response.data.models;
                        if (this.models.length > 0) {
                            this.selectedModel = this.models[0].id;
                        }
                    } catch (error) {
                        console.error('Error fetching models:', error);
                        this.models = [];
                    }
                },

                async setLLM() {
                    this.isSettingLLM = true;
                    try {
                        // Set the MCP protocol based on the selected provider
                        if (this.selectedProvider === 'mcp') {
                            this.mcpProtocol = 'SSE';
                        } else if (this.selectedProvider === 'mcp-http') {
                            this.mcpProtocol = 'HTTP';
                        } else {
                            this.mcpProtocol = '';
                        }

                        await axios.post('/gaia_chat/api/llm/set/', {
                            provider: this.selectedProvider,
                            model: this.selectedModel
                        });
                    } catch (error) {
                        console.error('Error setting LLM:', error);
                    } finally {
                        this.isSettingLLM = false;
                    }
                },

                async fetchConversations() {
                    try {
                        const response = await axios.get('/gaia_chat/api/conversations/');
                        this.conversations = response.data.conversations;
                    } catch (error) {
                        console.error('Error fetching conversations:', error);
                    }
                },

                async createConversation() {
                    this.isCreatingConversation = true;
                    try {
                        // Use the custom title if provided, otherwise use a default title
                        const title = this.newConversationTitle.trim()
                            ? this.newConversationTitle.trim()
                            : `Conversation ${new Date().toLocaleString()}`;

                        const response = await axios.post('/gaia_chat/api/conversations/create/', {
                            title: title
                        });

                        if (response.data.success) {
                            // Clear the title input
                            this.newConversationTitle = '';

                            // Refresh the conversation list and load the new conversation
                            await this.fetchConversations();
                            await this.loadConversation(response.data.conversation.id);
                        }
                    } catch (error) {
                        console.error('Error creating conversation:', error);
                    } finally {
                        this.isCreatingConversation = false;
                    }
                },

                async loadConversation(conversationId) {
                    this.isLoadingConversation = true;
                    this.error = null;
                    try {
                        const response = await axios.get(`/gaia_chat/api/conversations/${conversationId}/`);
                        if (response.data.success) {
                            const conversation = response.data.conversation;

                            // Check if the title is a default "Conversation" title
                            if (conversation.title && conversation.title.startsWith("Conversation ")) {
                                // Generate a better title from the first user message
                                const betterTitle = this.generateTitleFromMessages(conversation.messages);
                                if (betterTitle) {
                                    conversation.title = betterTitle;
                                }
                            }

                            this.activeConversation = conversation;

                            // Check if we need to update the MCP protocol info
                            if (this.selectedProvider === 'mcp') {
                                this.mcpProtocol = 'SSE';
                            } else if (this.selectedProvider === 'mcp-http') {
                                this.mcpProtocol = 'HTTP';
                            } else {
                                this.mcpProtocol = '';
                            }

                            this.scrollToBottom();
                        }
                    } catch (error) {
                        console.error('Error loading conversation:', error);
                        if (error.response && error.response.status === 403) {
                            // Permission error
                            this.error = "You do not have permission to access this conversation";
                        } else if (error.response && error.response.status === 404) {
                            // Not found error
                            this.error = "Conversation not found";
                        } else {
                            this.error = "Error loading conversation";
                        }
                    } finally {
                        this.isLoadingConversation = false;
                    }
                },

                async sendMessage() {
                    if (!this.userInput.trim() || !this.activeConversation) return;

                    const message = this.userInput;
                    this.userInput = '';
                    this.isSendingMessage = true;
                    this.error = null;

                    // Check if this is the first user message in a conversation with a default title
                    const isFirstUserMessage = this.activeConversation.messages.filter(msg => msg.role === 'user').length === 0;
                    const hasDefaultTitle = this.activeConversation.title && this.activeConversation.title.startsWith("Conversation ");

                    try {
                        const response = await axios.post('/gaia_chat/api/messages/send/', {
                            message: message
                        });

                        if (response.data.success) {
                            // Reload the conversation to get the updated messages
                            await this.loadConversation(this.activeConversation.id);

                            // If this was the first user message and the conversation has a default title,
                            // update the title based on the message content
                            if (isFirstUserMessage && hasDefaultTitle) {
                                // Generate a title from the first 4 words
                                const words = message.split(/\s+/).filter(word => word.trim().length > 0).slice(0, 4);
                                if (words.length > 0) {
                                    let newTitle = words.join(' ');
                                    // Add ellipsis if the message is longer than 4 words
                                    if (message.split(/\s+/).filter(word => word.trim().length > 0).length > 4) {
                                        newTitle += '...';
                                    }
                                    // Update the title in the UI
                                    if (this.activeConversation) {
                                        this.activeConversation.title = newTitle;
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        console.error('Error sending message:', error);
                        if (error.response && error.response.data.code === 'no_active_conversation') {
                            // Handle the case where there's no active conversation
                            this.activeConversation = null;
                            this.error = "No active conversation";
                        } else if (error.response && error.response.status === 403) {
                            // Permission error
                            this.error = "You do not have permission to send messages to this conversation";
                            this.activeConversation = null;
                        } else {
                            this.error = "Error sending message";
                        }
                    } finally {
                        this.isSendingMessage = false;
                    }
                },

                // Utility methods
                formatDate(dateString) {
                    if (!dateString) return '';
                    const date = new Date(dateString);
                    return date.toLocaleString();
                },

                formatRole(role) {
                    if (role === 'user') return "{{ user.username|default:'You'|escapejs }}";
                    if (role === 'assistant') return 'AgFunder Gaia';
                    if (role === 'system') return 'AgFunder Gaia';
                    return role;
                },

                formatMessage(content) {
                    // Use marked to render markdown
                    return marked.parse(content);
                },

                scrollToBottom() {
                    if (this.$refs.chatContainer) {
                        this.$refs.chatContainer.scrollTop = this.$refs.chatContainer.scrollHeight;
                    }
                },

                generateTitleFromMessages(messages) {
                    if (!messages || !messages.length) return null;

                    // Find the first user message
                    const userMessages = messages.filter(msg => msg.role === 'user');
                    if (!userMessages.length) return null;

                    const firstUserMessage = userMessages[0].content;
                    if (!firstUserMessage) return null;

                    // Extract the first 4 words (or fewer if the message is shorter)
                    const words = firstUserMessage.split(/\s+/).filter(word => word.trim().length > 0).slice(0, 4);
                    if (!words.length) return null;

                    let title = words.join(' ');

                    // Add ellipsis if the message is longer than 4 words
                    if (firstUserMessage.split(/\s+/).filter(word => word.trim().length > 0).length > 4) {
                        title += '...';
                    }

                    return title;
                },

                async deleteConversation(conversationId) {
                    this.isDeletingConversation = conversationId;
                    this.error = null;

                    try {
                        const response = await axios.delete(`/gaia_chat/api/conversations/${conversationId}/delete/`);

                        if (response.data.success) {
                            // If the deleted conversation was the active one, clear it
                            if (this.activeConversation && this.activeConversation.id === conversationId) {
                                this.activeConversation = null;
                            }

                            // Remove the conversation from the list
                            this.conversations = this.conversations.filter(conv => conv.id !== conversationId);
                        }
                    } catch (error) {
                        console.error('Error deleting conversation:', error);
                        if (error.response && error.response.status === 403) {
                            // Permission error
                            this.error = "You do not have permission to delete this conversation";
                        } else if (error.response && error.response.status === 404) {
                            // Not found error
                            this.error = "Conversation not found";
                            // Remove it from the list anyway
                            this.conversations = this.conversations.filter(conv => conv.id !== conversationId);
                        } else {
                            this.error = "Error deleting conversation";
                        }
                    } finally {
                        this.isDeletingConversation = null;
                    }
                }
            }
        });
    </script>
</body>
</html>
