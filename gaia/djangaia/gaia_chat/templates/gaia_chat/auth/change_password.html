{% extends 'gaia_chat/auth/base.html' %}

{% block title %}Change Password - Gaia Chat{% endblock %}

{% block header %}Change Password{% endblock %}
{% block subheader %}Update your password{% endblock %}

{% block content %}
<form method="post">
    {% csrf_token %}
    
    <div class="form-group">
        <label for="id_old_password">Current Password:</label>
        {{ form.old_password }}
        {% if form.old_password.errors %}
        <ul class="errorlist">
            {% for error in form.old_password.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label for="id_new_password1">New Password:</label>
        {{ form.new_password1 }}
        {% if form.new_password1.errors %}
        <ul class="errorlist">
            {% for error in form.new_password1.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
        <p class="help-text">{{ form.new_password1.help_text }}</p>
    </div>
    
    <div class="form-group">
        <label for="id_new_password2">Confirm New Password:</label>
        {{ form.new_password2 }}
        {% if form.new_password2.errors %}
        <ul class="errorlist">
            {% for error in form.new_password2.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
    </div>
    
    {% if form.non_field_errors %}
    <ul class="errorlist">
        {% for error in form.non_field_errors %}
        <li>{{ error }}</li>
        {% endfor %}
    </ul>
    {% endif %}
    
    <div class="form-group">
        <button type="submit" class="button">Change Password</button>
        <a href="{% url 'gaia_chat:profile' %}" class="button button-secondary">Back to Profile</a>
    </div>
</form>
{% endblock %}
