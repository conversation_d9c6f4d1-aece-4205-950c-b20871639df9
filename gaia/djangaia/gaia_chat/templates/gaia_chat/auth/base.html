<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gaia Chat{% endblock %}</title>
    <style>
        :root {
            --primary-color: #35424a;
            --secondary-color: #4a90e2;
            --background-color: #f4f4f4;
            --text-color: #333;
            --light-text: #666;
            --border-color: #ddd;
            --success-color: #4caf50;
            --error-color: #f44336;
            --warning-color: #ff9800;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            background-color: var(--background-color);
            color: var(--text-color);
            padding: 20px;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .header h1 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input[type="text"],
        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 16px;
        }
        
        .button {
            display: inline-block;
            padding: 10px 15px;
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
        }
        
        .button:hover {
            background-color: #3a7bc8;
        }
        
        .button-secondary {
            background-color: #6c757d;
        }
        
        .button-secondary:hover {
            background-color: #5a6268;
        }
        
        .messages {
            margin-bottom: 20px;
        }
        
        .message {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        
        .message-success {
            background-color: #e8f5e9;
            border-left: 4px solid var(--success-color);
        }
        
        .message-error {
            background-color: #ffebee;
            border-left: 4px solid var(--error-color);
        }
        
        .message-info {
            background-color: #e3f2fd;
            border-left: 4px solid var(--secondary-color);
        }
        
        .message-warning {
            background-color: #fff8e1;
            border-left: 4px solid var(--warning-color);
        }
        
        .help-text {
            font-size: 14px;
            color: var(--light-text);
            margin-top: 5px;
        }
        
        .errorlist {
            color: var(--error-color);
            list-style-type: none;
            padding: 0;
            margin: 5px 0;
        }
        
        .links {
            margin-top: 20px;
            text-align: center;
        }
        
        .links a {
            color: var(--secondary-color);
            text-decoration: none;
            margin: 0 10px;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{% block header %}Gaia Chat{% endblock %}</h1>
            <p>{% block subheader %}{% endblock %}</p>
        </div>
        
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="message message-{{ message.tags }}">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        {% block content %}{% endblock %}
        
        <div class="links">
            {% block links %}
            {% if user.is_authenticated %}
                <a href="{% url 'gaia_chat:chat_app' %}">Chat</a>
                <a href="{% url 'gaia_chat:profile' %}">Profile</a>
                <a href="{% url 'gaia_chat:logout' %}">Logout</a>
            {% else %}
                <a href="{% url 'gaia_chat:login' %}">Login</a>
                <a href="{% url 'gaia_chat:register' %}">Register</a>
            {% endif %}
            {% endblock %}
        </div>
    </div>
</body>
</html>
