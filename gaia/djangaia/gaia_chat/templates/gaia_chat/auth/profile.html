{% extends 'gaia_chat/auth/base.html' %}

{% block title %}Profile - Gaia Chat{% endblock %}

{% block header %}Profile{% endblock %}
{% block subheader %}Update your profile information{% endblock %}

{% block content %}
<form method="post">
    {% csrf_token %}
    
    <div class="form-group">
        <label for="id_username">Username:</label>
        {{ form.username }}
        {% if form.username.errors %}
        <ul class="errorlist">
            {% for error in form.username.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label for="id_email">Email:</label>
        {{ form.email }}
        {% if form.email.errors %}
        <ul class="errorlist">
            {% for error in form.email.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label for="id_first_name">First Name:</label>
        {{ form.first_name }}
        {% if form.first_name.errors %}
        <ul class="errorlist">
            {% for error in form.first_name.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label for="id_last_name">Last Name:</label>
        {{ form.last_name }}
        {% if form.last_name.errors %}
        <ul class="errorlist">
            {% for error in form.last_name.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
    </div>
    
    {% if form.non_field_errors %}
    <ul class="errorlist">
        {% for error in form.non_field_errors %}
        <li>{{ error }}</li>
        {% endfor %}
    </ul>
    {% endif %}
    
    <div class="form-group">
        <button type="submit" class="button">Update Profile</button>
        <a href="{% url 'gaia_chat:change_password' %}" class="button button-secondary">Change Password</a>
    </div>
</form>
{% endblock %}
