{% extends 'gaia_chat/auth/base.html' %}

{% block title %}Login - Gaia Chat{% endblock %}

{% block header %}Login{% endblock %}
{% block subheader %}Sign in to your account{% endblock %}

{% block content %}
<form method="post">
    {% csrf_token %}
    
    <div class="form-group">
        <label for="id_username">Username:</label>
        {{ form.username }}
        {% if form.username.errors %}
        <ul class="errorlist">
            {% for error in form.username.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label for="id_password">Password:</label>
        {{ form.password }}
        {% if form.password.errors %}
        <ul class="errorlist">
            {% for error in form.password.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
    </div>
    
    {% if form.non_field_errors %}
    <ul class="errorlist">
        {% for error in form.non_field_errors %}
        <li>{{ error }}</li>
        {% endfor %}
    </ul>
    {% endif %}
    
    <div class="form-group">
        <button type="submit" class="button">Login</button>
    </div>
    
    <div class="links">
        <a href="{% url 'gaia_chat:password_reset' %}">Forgot password?</a>
        <a href="{% url 'gaia_chat:register' %}">Create an account</a>
    </div>
</form>
{% endblock %}

{% block links %}{% endblock %}
