{% extends 'gaia_chat/auth/base.html' %}

{% block title %}Register - Gaia Chat{% endblock %}

{% block header %}Register{% endblock %}
{% block subheader %}Create a new account{% endblock %}

{% block content %}
<form method="post">
    {% csrf_token %}
    
    <div class="form-group">
        <label for="id_username">Username:</label>
        {{ form.username }}
        {% if form.username.errors %}
        <ul class="errorlist">
            {% for error in form.username.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
        <p class="help-text">{{ form.username.help_text }}</p>
    </div>
    
    <div class="form-group">
        <label for="id_email">Email:</label>
        {{ form.email }}
        {% if form.email.errors %}
        <ul class="errorlist">
            {% for error in form.email.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
    </div>
    
    <div class="form-group">
        <label for="id_first_name">First Name:</label>
        {{ form.first_name }}
        {% if form.first_name.errors %}
        <ul class="errorlist">
            {% for error in form.first_name.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
        <p class="help-text">{{ form.first_name.help_text }}</p>
    </div>
    
    <div class="form-group">
        <label for="id_last_name">Last Name:</label>
        {{ form.last_name }}
        {% if form.last_name.errors %}
        <ul class="errorlist">
            {% for error in form.last_name.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
        <p class="help-text">{{ form.last_name.help_text }}</p>
    </div>
    
    <div class="form-group">
        <label for="id_password1">Password:</label>
        {{ form.password1 }}
        {% if form.password1.errors %}
        <ul class="errorlist">
            {% for error in form.password1.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
        <p class="help-text">{{ form.password1.help_text }}</p>
    </div>
    
    <div class="form-group">
        <label for="id_password2">Confirm Password:</label>
        {{ form.password2 }}
        {% if form.password2.errors %}
        <ul class="errorlist">
            {% for error in form.password2.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
        <p class="help-text">{{ form.password2.help_text }}</p>
    </div>
    
    {% if form.non_field_errors %}
    <ul class="errorlist">
        {% for error in form.non_field_errors %}
        <li>{{ error }}</li>
        {% endfor %}
    </ul>
    {% endif %}
    
    <div class="form-group">
        <button type="submit" class="button">Register</button>
    </div>
    
    <div class="links">
        <a href="{% url 'gaia_chat:login' %}">Already have an account? Login</a>
    </div>
</form>
{% endblock %}

{% block links %}{% endblock %}
