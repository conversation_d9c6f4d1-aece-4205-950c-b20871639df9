{% extends 'gaia_chat/auth/base.html' %}

{% block title %}Set New Password - Gaia Chat{% endblock %}

{% block header %}Set New Password{% endblock %}
{% block subheader %}Enter your new password{% endblock %}

{% block content %}
{% if validlink %}
<form method="post">
    {% csrf_token %}
    
    <div class="form-group">
        <label for="id_new_password1">New Password:</label>
        {{ form.new_password1 }}
        {% if form.new_password1.errors %}
        <ul class="errorlist">
            {% for error in form.new_password1.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
        <p class="help-text">{{ form.new_password1.help_text }}</p>
    </div>
    
    <div class="form-group">
        <label for="id_new_password2">Confirm New Password:</label>
        {{ form.new_password2 }}
        {% if form.new_password2.errors %}
        <ul class="errorlist">
            {% for error in form.new_password2.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
        {% endif %}
    </div>
    
    {% if form.non_field_errors %}
    <ul class="errorlist">
        {% for error in form.non_field_errors %}
        <li>{{ error }}</li>
        {% endfor %}
    </ul>
    {% endif %}
    
    <div class="form-group">
        <button type="submit" class="button">Set Password</button>
    </div>
</form>
{% else %}
<div class="message message-error">
    <p>The password reset link was invalid, possibly because it has already been used. Please request a new password reset.</p>
</div>

<div class="links">
    <a href="{% url 'gaia_chat:password_reset' %}">Request New Reset Link</a>
</div>
{% endif %}
{% endblock %}

{% block links %}{% endblock %}
