import streamlit as st
import pandas as pd
import os
import sys
import uuid
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Protocol

# Import these conditionally in the functions to avoid dependency issues
# import openai
# import anthropic


# Add the necessary paths to import CETO components
sys.path.append("../..")
from gaia.gaia_ceto.ceto_v002.chatobj import ChatManager, Conversation, MockLLM, OpenAILLM, AnthropicLLM
from gaia.gaia_ceto.ceto_v002.chrono_store import ChronoStore

# Set up logging
logger = logging.getLogger(__name__)

# Helper functions to get available models with caching
@st.cache_data(ttl=3600)  # Cache for 1 hour
def get_openai_models():
    """Get available models from OpenAI API with caching."""
    return OpenAILLM.get_available_models()

@st.cache_data(ttl=3600)  # Cache for 1 hour
def get_anthropic_models():
    """Get available models from Anthropic API with caching."""
    return AnthropicLLM.get_available_models()


# Factory function to create LLM instances
def create_llm(llm_type: str = "mock", **kwargs):
    """Create an LLM instance based on the specified type.

    Args:
        llm_type: The type of LLM to create ("mock", "openai", or "anthropic").
        **kwargs: Additional parameters to pass to the LLM constructor.

    Returns:
        An instance of the specified LLM.

    Raises:
        ValueError: If the specified LLM type is not supported.
    """
    if llm_type.lower() == "mock":
        return MockLLM()
    elif llm_type.lower() == "openai":
        return OpenAILLM(**kwargs)
    elif llm_type.lower() == "anthropic":
        return AnthropicLLM(**kwargs)
    else:
        raise ValueError(f"Unsupported LLM type: {llm_type}")


def initialize_chat_manager(llm_type="mock", model_name=None):
    """Initialize the chat manager with the appropriate storage directory and LLM.

    Args:
        llm_type: The type of LLM to use ("mock", "openai", or "anthropic").
        model_name: The specific model name to use (if applicable).

    Returns:
        A ChatManager instance.
    """
    # Use a persistent storage directory
    storage_dir = "/var/lib/gaia/GAIA_FS/ceto_conversations"

    # Ensure the directory exists
    os.makedirs(storage_dir, exist_ok=True)

    # Create the LLM instance with appropriate parameters
    kwargs = {}
    if model_name:
        kwargs["model_name"] = model_name

    # Create the LLM instance
    llm = create_llm(llm_type, **kwargs)

    # Create and return the chat manager
    return ChatManager(storage_dir=storage_dir, llm=llm)


def format_conversation_list(conversations: List[Dict[str, Any]]) -> pd.DataFrame:
    """Format the conversation list for display in a DataFrame."""
    if not conversations:
        return pd.DataFrame(columns=["ID", "Title", "Created", "Messages", "Path"])

    # Extract relevant information for display
    data = []
    for conv in conversations:
        data.append({
            "ID": conv["conversation_id"],
            "Title": conv["title"],
            "Created": conv["created_at"],
            "Messages": conv["message_count"],
            "Path": conv["relative_path"]
        })

    return pd.DataFrame(data)


def display_conversation(conversation: Conversation):
    """Display a conversation in a chat-like interface."""
    if not conversation.messages:
        st.info("This conversation has no messages yet.")
        return

    for msg in conversation.messages:
        role = msg["role"]
        content = msg["content"]

        if role == "user":
            st.markdown(f"**You:** {content}")
        elif role == "assistant":
            st.markdown(f"**Assistant:** {content}")
        elif role == "system":
            st.markdown(f"**System:** {content}")


def main():
    st.set_page_config(layout="wide", page_title="CETO Chat")

    # Initialize session states
    if "llm_type" not in st.session_state:
        st.session_state.llm_type = "mock"

    if "model_name" not in st.session_state:
        st.session_state.model_name = None

    if "active_conversation" not in st.session_state:
        st.session_state.active_conversation = None

    # Page title and description
    st.title("🤖 CETO Chat")
    st.markdown("""
    CETO is an enhanced chat system with chronological storage for thousands of conversations.
    This interface allows you to create, manage, and use conversations through a web interface.
    """)

    # Sidebar for conversation management and settings
    with st.sidebar:
        st.header("Select an LLM provider and Model")

        # LLM selection
        llm_options = ["mock", "openai", "anthropic"]
        selected_llm = st.selectbox(
            "Select LLM Provider",
            options=llm_options,
            index=llm_options.index(st.session_state.llm_type),
            help="Choose the LLM provider to use for generating responses.",
            key="llm_provider"
        )

        # Get available models based on selected provider
        if selected_llm == "openai":
            with st.spinner("Loading OpenAI models..."):
                available_models = get_openai_models()
        elif selected_llm == "anthropic":
            with st.spinner("Loading Anthropic models..."):
                available_models = get_anthropic_models()
        else:
            available_models = ["Default Mock"]

        # Model selection based on available models
        model_options = {
            "mock": ["Default Mock"],
            "openai": available_models if selected_llm == "openai" else ["gpt-3.5-turbo"],
            "anthropic": available_models if selected_llm == "anthropic" else ["claude-3-sonnet-20240229"]
        }

        # If the previously selected model is not in the list, default to the first one
        if st.session_state.model_name not in model_options[selected_llm]:
            default_index = 0
        else:
            default_index = model_options[selected_llm].index(st.session_state.model_name)

        selected_model = st.selectbox(
            "Select Model",
            options=model_options[selected_llm],
            index=default_index,
            help="Choose the specific model to use.",
            key="llm_model"
        )

        # Only use the model name for real LLMs
        if selected_llm != "mock" and selected_model != "Default Mock":
            model_name = selected_model
        else:
            model_name = None

        # Check if LLM type or model has changed
        llm_changed = (selected_llm != st.session_state.llm_type or
                      model_name != st.session_state.model_name)

        # Display current API key status
        if selected_llm == "openai":
            api_key = os.environ.get("OPENAI_API_KEY")
            if not api_key:
                st.error("OpenAI API key not found. Please set the OPENAI_API_KEY environment variable.")

        elif selected_llm == "anthropic":
            api_key = os.environ.get("ANTHROPIC_API_KEY")
            if not api_key:
                st.error("Anthropic API key not found. Please set the ANTHROPIC_API_KEY environment variable.")

        # Update chat manager if LLM settings have changed
        if llm_changed:
            # Update session state
            st.session_state.llm_type = selected_llm
            st.session_state.model_name = model_name

            # Reinitialize chat manager with new settings
            try:
                st.session_state.chat_manager = initialize_chat_manager(
                    llm_type=selected_llm,
                    model_name=model_name
                )
                #st.success(f"Using {selected_llm} LLM" +
                #          (f" with {model_name}" if model_name else ""))
            except Exception as e:
                st.error(f"Failed to initialize {selected_llm} LLM: {str(e)}")
                # Fallback to mock LLM
                st.session_state.llm_type = "mock"
                st.session_state.model_name = None
                st.session_state.chat_manager = initialize_chat_manager(
                    llm_type="mock"
                )
                st.warning("Falling back to mock LLM")

        # Initialize chat manager if it doesn't exist
        if "chat_manager" not in st.session_state:
            st.session_state.chat_manager = initialize_chat_manager(
                llm_type=st.session_state.llm_type,
                model_name=st.session_state.model_name
            )

        st.header("Conversation Management")

        # Create a new conversation
        st.subheader("New Conversation")
        new_title = st.text_input("Conversation Title",
                                 value=f"Conversation {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        if st.button("New Conversation"):
            # Create a new conversation
            conversation = st.session_state.chat_manager.create_conversation(
                title=new_title,
                user_id=os.getenv("USER", "streamlit_user")
            )

            # Add a system message to start
            st.session_state.chat_manager.add_message("system", "Welcome to GaiaChat! How can I help you today?")

            # Save the conversation
            st.session_state.chat_manager.save_conversation()

            # Set as active conversation
            st.session_state.active_conversation = conversation

            st.success(f"Created new conversation: {conversation.title}")
            st.rerun()

        # List existing conversations
        st.subheader("Previous Conversations")

        # Get the list of conversations
        conversations = st.session_state.chat_manager.list_conversations()

        if not conversations:
            st.info("No conversations found. Create a new one to get started.")
        else:
            # Format the conversations for display
            df = format_conversation_list(conversations)

            # Display the conversations in a selectbox
            selected_id = st.selectbox(
                "Select a conversation",
                options=df["ID"].tolist(),
                format_func=lambda x: f"{df[df['ID'] == x]['Title'].iloc[0]} ({x[:8]}...)"
            )

            if st.button("Load Conversation"):
                # Load the selected conversation
                conversation = st.session_state.chat_manager.load_conversation(selected_id)

                if conversation:
                    st.session_state.active_conversation = conversation
                    st.success(f"Loaded conversation: {conversation.title}")
                    st.rerun()
                else:
                    st.error("Failed to load conversation.")

        # Display conversation statistics
        if st.checkbox("Show Statistics"):
            stats = st.session_state.chat_manager.get_conversation_stats()
            st.write(f"Total conversations: {stats['total_conversations']}")

            st.write("By Year/Month:")
            for year, year_data in stats["years"].items():
                st.write(f"  {year}: {year_data['total']} conversations")

    # Main chat interface
    if st.session_state.active_conversation:
        conversation = st.session_state.active_conversation

        # Display conversation info
        st.subheader(f"Conversation: {conversation.title}")
        st.write(f"ID: {conversation.conversation_id}")
        st.write(f"Created: {conversation.created_at}")

        # Display the conversation
        st.subheader("Messages")
        display_conversation(conversation)

        # Input for new messages
        st.subheader("New Message")
        user_input = st.text_area("Type your message here", height=100)

        # Add a status indicator for the current LLM
        st.info(f"Using {st.session_state.llm_type} LLM" +
                (f" with {st.session_state.model_name}" if st.session_state.model_name else ""))

        if st.button("Send"):
            if user_input.strip():
                with st.spinner("Generating response..."):
                    try:
                        # Process the message and get the response
                        _ = st.session_state.chat_manager.process_message(user_input)

                        # Save the conversation
                        st.session_state.chat_manager.save_conversation()

                        # Show success message
                        st.success("Message sent and response received!")

                        # Refresh the UI to show the new message
                        st.rerun()
                    except Exception as e:
                        st.error(f"Error processing message: {str(e)}")
    else:
        st.info("No active conversation. Create a new one or load an existing one from the sidebar.")


if __name__ == "__main__":
    main()
