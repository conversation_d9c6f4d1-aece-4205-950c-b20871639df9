# Context SpiderELF Analysis

This folder contains utilities for running a SpiderELF analysis workflow on a
given context configuration. The legacy `run_all.sh` script sequentially calls
multiple numbered Python scripts. When run with a configuration slug (for
example `food_waste_valor_ferment`), the steps are executed in the following
order:

1. `s100_run_spiderelf.py`
2. `s105_run_spiderelf_extra.py`
3. `s120_combine_cb_spiderelf.py`
4. `s130_run_dimension_var.py`
5. `s150_run_analysis_elves.py`
6. `s155_run_analysis_elves_rating.py`
7. `s160_merger.py`
8. `s165_taxonomy_elf.py`
9. `s170_merger2.py`
10. `s180_slack_post.py`

The legacy workflow is typically invoked as:

```bash
./run_all.sh food_waste_valor_ferment
```
which begins by refreshing agsearch contexts via
`gaia_frames/d010_frames_import/s010_import__0002_import_agsearch_contexts.py`.

Configuration YAML files live in the `configs/` directory. Each YAML specifies
`context_id`, `context_slug`, optional `add_sites`, and rating definitions.

To run the consolidated workflow directly:

```bash
python x100__context_attributes_analysis_agent.py food_waste_valor_ferment
```

Each step writes intermediate CSV files inside a folder named after the
`context_slug` from the configuration.

The new consolidated script `x100__context_attributes_analysis_agent.py`
replaces these numbered scripts and performs the same operations in a single
run.
