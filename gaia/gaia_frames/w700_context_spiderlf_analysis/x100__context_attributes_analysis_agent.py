#!/usr/bin/env python
"""Run the full context SpiderELF analysis in one step.

This script consolidates the functionality of the numbered scripts previously
invoked by ``run_all.sh``. It produces the same intermediate CSV files.
"""

import os
import sys
import shutil
import yaml
import pandas as pd

from gaia.gaia_frames import gaia_frames
from gaia.gaia_elf.smartspider import spiderelf_org_lib
from gaia.gaia_elf.company_variant_elf import (
    company_variant_elf_02 as company_variant_elf,
    company_dim_elf_mproc,
    taxonomy_classifier_elf,
)
from gaia.gaia_elf.classify_elf import rating_elf_large_mproc

import context_spiderelf

# When True, intermediate CSV files are written to disk.
INTERMED_FILES = True


def ensure_folder_exists(folder_path: str) -> None:
    """Create ``folder_path`` if it does not already exist."""
    os.makedirs(folder_path, exist_ok=True)


def step_s100_run_spiderelf(
    context_id: int, context_slug: str
) -> pd.DataFrame:
    """Run SpiderELF on all organizations for a context.

    Args:
        context_id: Numeric identifier for the context.
        context_slug: Name used for the output folder.

    Returns:
        DataFrame of SpiderELF results.
    """

    llm, spider_elf, _ = spiderelf_org_lib.init_worker()

    cont = gaia_frames.read_frame_pandas(
        section_slug="agsearch", frame_slug="agsearch_asrating", cols=None
    )
    cont = cont[cont.context_id == context_id]
    cont = cont[cont.rating == 1]
    cb_ids = cont.es_id.unique().tolist()

    cols = ["org_uuid", "org_domain", "org_homepage_url"]
    orgs = gaia_frames.read_frame_pandas(
        section_slug="cb_aug", frame_slug="orgs_geo", cols=cols
    )
    orgs = orgs[orgs.org_uuid.isin(cb_ids)]

    domains_or_urls = orgs["org_homepage_url"]
    spiderelf_results_df = spiderelf_org_lib.spiderelf_domain_list_mproc(
        domains_or_urls, k=70
    )
    spiderelf_results_df["org_uuid"] = orgs.org_uuid.tolist()

    if INTERMED_FILES:
        ensure_folder_exists(context_slug)
        ensure_folder_exists(os.path.join(context_slug, "uuid"))
        ensure_folder_exists(os.path.join(context_slug, "dom"))
        ensure_folder_exists(os.path.join(context_slug, "gen"))
        fn = os.path.join(context_slug, "uuid", "s100_spider_elf.csv")
        spiderelf_results_df.to_csv(fn, index=False)

    return spiderelf_results_df


def step_s105_spiderelf_extra(
    add_sites: list[str] | None, context_slug: str
) -> pd.DataFrame | None:
    """Run SpiderELF on manually specified domains.

    Args:
        add_sites: Optional list of additional domains to crawl.
        context_slug: Output folder name.

    Returns:
        DataFrame of SpiderELF results for the extra sites or ``None``.
    """

    if not add_sites:
        return None

    extra_spiderelf_results_df = spiderelf_org_lib.spiderelf_domain_list_mproc(
        add_sites
    )
    if INTERMED_FILES:
        fn = os.path.join(context_slug, "dom", "s105_spider_elf_by_domain.csv")
        extra_spiderelf_results_df.to_csv(fn, index=False)

    return extra_spiderelf_results_df


def step_s120_combine_cb_spiderelf(
    spiderelf_df: pd.DataFrame,
    extra_spider_df: pd.DataFrame | None,
    context_id: int,
    context_slug: str,
) -> pd.DataFrame:
    """Merge Crunchbase data with SpiderELF results.

    Args:
        spiderelf_df: Results from :func:`step_s100_run_spiderelf`.
        extra_spider_df: Optional extra SpiderELF results.
        context_id: Identifier for the context.
        context_slug: Output folder.

    Returns:
        Combined DataFrame with descriptions and SpiderELF facts.
    """

    cont = gaia_frames.read_frame_pandas(
        section_slug="agsearch", frame_slug="agsearch_asrating", cols=None
    )
    cont = cont[cont.rating == 1]
    cont = cont[cont.context_id == context_id]
    cb_ids = cont.es_id.tolist()

    cols = [
        "uuid",
        "short_description",
        "description",
        "domain",
        "country_code",
        "state_code",
        "city",
        "status",
        "total_funding_usd_mm",
        "num_funding_rounds",
        "num_exits",
        "founded_on",
        "last_funding_on",
        "employee_count",
    ]
    orgs = gaia_frames.read_frame_pandas(
        section_slug="cb_aug", frame_slug="orgs_w_descriptions", cols=cols
    )
    orgs = orgs[orgs.uuid.isin(cb_ids)]
    orgs["descr_full"] = (
        orgs["short_description"].fillna("").astype(str)
        + " "
        + orgs["description"].fillna("").astype(str)
    )
    orgs = orgs.drop_duplicates()

    scores_team = gaia_frames.read_frame_pandas(
        section_slug="cb_aug", frame_slug="org_alljob_eduscores_current_agg"
    )
    scores_org_i2i = gaia_frames.read_frame_pandas(
        section_slug="graphs/i2i", frame_slug="orgs_i2i_max"
    )
    merge_df = pd.merge(
        orgs,
        scores_org_i2i,
        left_on="uuid",
        right_on="fr_org_uuid",
        how="left",
    )
    merge_df = pd.merge(
        merge_df, scores_team, left_on="uuid", right_on="org_uuid", how="left"
    )
    orgs = merge_df.drop(orgs.filter(like="edu_fld_").columns, axis=1)

    if INTERMED_FILES:
        fn = os.path.join(context_slug, "uuid", "s120_orgs_cb.csv")
        orgs.to_csv(fn, index=False)

    merged_df = pd.merge(
        orgs, spiderelf_df, left_on="uuid", right_on="org_uuid", how="left"
    )

    if extra_spider_df is not None:
        extra_spider_df_aligned = extra_spider_df.reindex(
            columns=merged_df.columns
        )
        df_combined = pd.concat(
            [merged_df, extra_spider_df_aligned], ignore_index=True
        )
    else:
        df_combined = merged_df

    df_combined["descr_ext"] = (
        df_combined["descr_full"].fillna("").astype(str)
        + " "
        + df_combined["facts"].fillna("").astype(str)
    )

    if INTERMED_FILES:
        fn = os.path.join(context_slug, "uuid", "s120_source_merged_df.csv")
        df_combined.to_csv(fn, index=False)

    return df_combined


def step_s130_run_dimension_var(orgs: pd.DataFrame, context_slug: str) -> dict:
    """Extract dimensions of variation from descriptions.

    Args:
        orgs: DataFrame produced by :func:`step_s120_combine_cb_spiderelf`.
        context_slug: Output folder name.

    Returns:
        Mapping of dimension labels to text spans.
    """

    company_descriptions = orgs["descr_ext"].tolist()
    company_ids = orgs["dom"]
    company_descriptions_dict = dict(zip(company_ids, company_descriptions))

    processor = company_variant_elf.CompanyDescriptionProcessor(
        model="o1-preview"
    )
    dimensions_of_variation = processor.extract_dimensions(
        company_descriptions[:20]
    )

    if INTERMED_FILES:
        yaml_file = os.path.join(
            context_slug, "gen", "s150_dimensions_of_variation.yaml"
        )
        with open(yaml_file, "w") as file:
            yaml.dump(
                {"dimensions_of_variation": dimensions_of_variation},
                file,
                default_flow_style=False,
            )
        df = pd.DataFrame(
            list(dimensions_of_variation.items()), columns=["key", "value"]
        )
        df.to_csv(
            os.path.join(
                context_slug, "gen", "s150_dimensions_of_variation.csv"
            ),
            index=False,
        )

    return dimensions_of_variation


def step_s150_run_analysis_elves(
    orgs: pd.DataFrame, dimensions_of_variation: dict, context_slug: str
) -> pd.DataFrame:
    """Generate dimension-level metrics using Company Dimension ELF.

    Args:
        orgs: DataFrame from :func:`step_s120_combine_cb_spiderelf`.
        dimensions_of_variation: Mapping returned from :func:`step_s130_run_dimension_var`.
        context_slug: Output folder name.

    Returns:
        DataFrame of dimension metrics.
    """

    company_descriptions = orgs["descr_ext"].tolist()
    company_ids = orgs["dom"]
    company_descriptions_dict = dict(zip(company_ids, company_descriptions))

    processor = company_dim_elf_mproc.CompanyDimensionElfMproc(
        model="o1-preview", max_tokens=32000
    )
    batch_size = 20
    dimension_dfs = []
    keys = list(company_descriptions_dict.keys())
    for i in range(0, len(keys), batch_size):
        chunk_keys = keys[i : i + batch_size]
        batch_dict = {
            key: company_descriptions_dict[key] for key in chunk_keys
        }
        dimension_df = processor.extract_dimension_data(
            batch_dict, dimensions_of_variation
        )
        dimension_dfs.append(dimension_df)

    full_dimension_df = pd.concat(dimension_dfs, ignore_index=True)
    if INTERMED_FILES:
        fn = os.path.join(context_slug, "uuid", "s150_dimension_df.csv")
        full_dimension_df.to_csv(fn, index=False)

    return full_dimension_df


def step_s155_run_analysis_elves_rating(
    orgs: pd.DataFrame, ratings_defn: dict, context_slug: str
) -> pd.DataFrame:
    """Rate company descriptions using TextRaterELF.

    Args:
        orgs: DataFrame from :func:`step_s120_combine_cb_spiderelf`.
        ratings_defn: Rating definitions from the config file.
        context_slug: Output folder name.

    Returns:
        DataFrame of rating scores.
    """

    company_descriptions = orgs["descr_ext"].tolist()

    rater = rating_elf_large_mproc.TextRaterElfLargeMproc(
        model="o1-preview", chunk_size=20, k=10
    )
    ratings_df = rater.rate_texts(company_descriptions, ratings_defn)

    if INTERMED_FILES:
        fn = os.path.join(context_slug, "uuid", "s155_ratings_df.csv")
        ratings_df.to_csv(fn, index=False)

    return ratings_df


def step_s160_merger(
    orgs: pd.DataFrame,
    ratings_df: pd.DataFrame,
    dimension_df: pd.DataFrame,
    context_slug: str,
) -> pd.DataFrame:
    """Merge ratings and dimension data into a single DataFrame.

    Args:
        orgs: Output from :func:`step_s120_combine_cb_spiderelf`.
        ratings_df: DataFrame from :func:`step_s155_run_analysis_elves_rating`.
        dimension_df: DataFrame from :func:`step_s150_run_analysis_elves`.
        context_slug: Output folder name.

    Returns:
        DataFrame ready for taxonomy classification.
    """

    orgs = pd.concat([orgs, ratings_df], axis=1)
    merged_df = pd.merge(
        orgs, dimension_df, left_on="dom", right_on="company_id", how="left"
    )
    columns_to_drop = [
        col for col in merged_df.columns if col.startswith("Unnamed")
    ]
    merged_df = merged_df.drop(columns=columns_to_drop).drop_duplicates()
    merged_df = merged_df[~merged_df.dom.isna()]
    merged_df = merged_df.sort_values(by="rating_hybrid", ascending=False)

    if INTERMED_FILES:
        fn = os.path.join(context_slug, "uuid", "s160_premerge.csv")
        merged_df.to_csv(fn, index=False)

    return merged_df


def step_s165_taxonomy_elf(
    premerge_df: pd.DataFrame, context_slug: str
) -> tuple[pd.DataFrame, pd.DataFrame]:
    """Classify companies into a taxonomy hierarchy.

    Args:
        premerge_df: DataFrame from :func:`step_s160_merger`.
        context_slug: Output folder name.

    Returns:
        Tuple of taxonomy DataFrame and indicator DataFrame.
    """
    orgs = premerge_df

    def dataframe_to_text_list_optimized(
        df: pd.DataFrame, columns: list
    ) -> list:
        return [
            "; ".join(
                f"{col}: {row[col]}"
                for col in columns
                if pd.notnull(row[col]) and row[col] != ""
            )
            for _, row in df.iterrows()
        ]

    cols = [
        "short_description",
        "d3l",
        "prd",
        "cust",
        "meth",
        "tech",
        "compet",
    ]
    text_list = dataframe_to_text_list_optimized(orgs, cols)
    ids = orgs["company_id"].to_list()
    company_dict = dict(zip(ids, text_list))

    taxonomy_writer = taxonomy_classifier_elf.TaxonomyWriterRecurseElf(
        model="o1-preview",
        depth=3,
        max_tokens=16000,
        max_retries=3,
        max_iterations=3,
    )
    taxonomy_classifier = taxonomy_classifier_elf.TaxonomyClassifierElf(
        model="o1-preview", max_tokens=16000, max_retries=3
    )
    taxonomy_results = taxonomy_classifier_elf.do_taxonomy(
        company_dict, taxonomy_writer, taxonomy_classifier
    )

    indicator_df = taxonomy_results.get("df_indicator")
    indicator_df = indicator_df.reset_index().rename(
        columns={"index": "company_id"}
    )
    taxonomy = taxonomy_results.get("tax")
    taxonomy_df = pd.DataFrame(taxonomy).reset_index()

    if INTERMED_FILES:
        fn = os.path.join(context_slug, "uuid", "s165_taxonomy_df.csv")
        taxonomy_df.T.reset_index().to_csv(fn, index=False)
        fn_indicator = os.path.join(
            context_slug, "uuid", "s165_taxonomy_df_indicator.csv"
        )
        indicator_df.to_csv(fn_indicator, index=False)

    return taxonomy_df, indicator_df


def step_s170_merger2(
    premerge_df: pd.DataFrame, indicator_df: pd.DataFrame, context_slug: str
) -> pd.DataFrame:
    """Merge taxonomy indicators with premerged data.

    Args:
        premerge_df: Output from :func:`step_s160_merger`.
        indicator_df: Indicator DataFrame from :func:`step_s165_taxonomy_elf`.
        context_slug: Output folder name.

    Returns:
        Final merged DataFrame.
    """

    merged_df = pd.merge(
        premerge_df,
        indicator_df,
        left_on="company_id",
        right_on="company_id",
        how="left",
    )

    if INTERMED_FILES:
        fn = os.path.join(context_slug, "uuid", "s170_merge_df.csv")
        merged_df.to_csv(fn, index=False)
        output = os.path.join(
            context_slug, "gen", f"context_elf_analysis__{context_slug}.csv"
        )
        try:
            shutil.copy(fn, output)
        except Exception as e:
            print(f"An error occurred: {e}")

    return merged_df


def convert_csv_to_xlsx(fn_csv_in: str, fn_xlsx_out: str) -> None:
    """Convert a CSV file to XLSX format."""

    try:
        df = pd.read_csv(fn_csv_in)
        df.to_excel(fn_xlsx_out, index=False, engine="openpyxl")
    except FileNotFoundError:
        print(f"File {fn_csv_in} not found.")
    except Exception as e:
        print(f"An error occurred: {e}")


def step_s180_slack_post(csv_fn: str, context_slug: str) -> None:
    """Convert result CSV to XLSX and post to Slack.

    Args:
        csv_fn: Path to the CSV file to post.
        context_slug: Name of the context for labeling.
    """

    from gaia.comm.slack import lib_slack

    xlsx_fn = csv_fn + ".xlsx"
    convert_csv_to_xlsx(csv_fn, xlsx_fn)
    chan = "gaia-reports"
    lib_slack.slack_post_file(
        chan=chan,
        fil=xlsx_fn,
        title=context_slug + ":Gaia SpiderElf Context v0.3",
    )


def main() -> None:
    if len(sys.argv) < 2:
        print(
            "Usage: x100__context_attributes_analysis_agent.py <config_slug>"
        )
        raise SystemExit(1)

    config_slug = sys.argv[1]
    config = context_spiderelf.read_config(slug=config_slug)

    context_id = config["context_id"]
    context_slug = config["context_slug"]
    add_sites = config.get("add_sites")
    ratings_defn = config.get("ratings_defn", {})

    spider_df = step_s100_run_spiderelf(context_id, context_slug)
    extra_df = step_s105_spiderelf_extra(add_sites, context_slug)
    orgs = step_s120_combine_cb_spiderelf(
        spider_df, extra_df, context_id, context_slug
    )
    dims = step_s130_run_dimension_var(orgs, context_slug)
    dim_df = step_s150_run_analysis_elves(orgs, dims, context_slug)
    rating_df = step_s155_run_analysis_elves_rating(
        orgs, ratings_defn, context_slug
    )
    premerge_df = step_s160_merger(orgs, rating_df, dim_df, context_slug)
    taxonomy_df, indicator_df = step_s165_taxonomy_elf(
        premerge_df, context_slug
    )
    final_df = step_s170_merger2(premerge_df, indicator_df, context_slug)

    if INTERMED_FILES:
        output = os.path.join(
            context_slug, "gen", f"context_elf_analysis__{context_slug}.csv"
        )
        final_df.to_csv(output, index=False)
    else:
        output = os.path.join(
            context_slug, "gen", f"context_elf_analysis__{context_slug}.csv"
        )

    step_s180_slack_post(output, context_slug)


if __name__ == "__main__":
    main()
