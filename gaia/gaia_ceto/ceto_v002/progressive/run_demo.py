#!/usr/bin/env python3
"""
Quick Demo Runner for Progressive MCP

This script makes it easy to start both server and client for testing.
"""

import asyncio
import subprocess
import sys
import time
import os
from pathlib import Path

def print_banner():
    """Print a nice banner."""
    print("=" * 60)
    print("🚀 Progressive MCP - Streaming Demo")
    print("=" * 60)
    print("Real-time progress updates from MCP tools to clients!")
    print()

def check_dependencies():
    """Check if required dependencies are available."""
    try:
        import websockets
        print("✅ websockets library available")
        return True
    except ImportError:
        print("❌ websockets library not found")
        print("   Install with: pip install websockets")
        return False

async def run_server_and_client():
    """Run both server and client for demo."""
    print_banner()
    
    if not check_dependencies():
        return
    
    print("🎯 Demo Options:")
    print("1. Start server only")
    print("2. Start client only (server must be running)")
    print("3. Start both server and client")
    print("0. Exit")
    
    choice = input("\nSelect option (0-3): ").strip()
    
    if choice == "0":
        print("👋 Goodbye!")
        return
    elif choice == "1":
        await start_server()
    elif choice == "2":
        await start_client()
    elif choice == "3":
        await start_both()
    else:
        print("❌ Invalid choice")

async def start_server():
    """Start the demo server."""
    print("\n🚀 Starting Progressive MCP Demo Server...")
    print("📡 Server will run on ws://localhost:8765")
    print("🛑 Press Ctrl+C to stop")
    print("-" * 40)
    
    try:
        # Import and run server
        from .demo_server import main as server_main
        await server_main()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")

async def start_client():
    """Start the demo client."""
    print("\n📱 Starting Progressive MCP Demo Client...")
    print("📡 Connecting to ws://localhost:8765")
    print("-" * 40)
    
    try:
        # Import and run client
        from .demo_client import main as client_main
        await client_main()
    except KeyboardInterrupt:
        print("\n🛑 Client stopped by user")
    except Exception as e:
        print(f"❌ Client error: {e}")

async def start_both():
    """Start both server and client."""
    print("\n🚀 Starting both server and client...")
    print("📡 Server: ws://localhost:8765")
    print("📱 Client: Will connect automatically")
    print("🛑 Press Ctrl+C to stop both")
    print("-" * 40)
    
    try:
        # Start server in background
        from .demo_server import create_demo_server
        server = create_demo_server()
        
        print("🚀 Starting server...")
        await server.start_server("localhost", 8765)
        
        # Give server time to start
        await asyncio.sleep(1)
        
        print("📱 Starting client...")
        
        # Start client
        from .demo_client import DemoClient
        client = DemoClient("ws://localhost:8765")
        
        # Run client in a task so we can handle server cleanup
        client_task = asyncio.create_task(client.start())
        
        try:
            await client_task
        except KeyboardInterrupt:
            print("\n🛑 Stopping client...")
            client_task.cancel()
        
        print("🛑 Stopping server...")
        await server.stop_server()
        
    except KeyboardInterrupt:
        print("\n🛑 Stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main entry point."""
    try:
        asyncio.run(run_server_and_client())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
