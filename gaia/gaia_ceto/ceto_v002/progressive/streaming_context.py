#!/usr/bin/env python3
"""
Streaming Context for Progressive MCP

This module provides the StreamingContext class that enables real-time
progress updates from MCP tools to clients via WebSocket connections.
"""

import json
import time
import asyncio
from typing import Optional, Any, Dict
import logging

logger = logging.getLogger(__name__)


class StreamingContext:
    """Context object that enables real-time streaming from MCP tools to clients.
    
    This context is injected into streaming-capable MCP tools and provides
    methods to send progress updates, info messages, and other real-time
    data to the connected client.
    """
    
    def __init__(self, stream_id: str, websocket, client_info: Optional[Dict] = None):
        """Initialize the streaming context.
        
        Args:
            stream_id: Unique identifier for this stream
            websocket: WebSocket connection to the client
            client_info: Optional information about the client
        """
        self.stream_id = stream_id
        self.websocket = websocket
        self.client_info = client_info or {}
        self.start_time = time.time()
        self.message_count = 0
        self.is_closed = False
        
    async def report_progress(self, progress: int, total: int, message: str = "") -> bool:
        """Send a progress update to the client.
        
        Args:
            progress: Current progress (0 to total)
            total: Total number of items/steps
            message: Optional descriptive message
            
        Returns:
            True if message was sent successfully, False otherwise
        """
        if self.is_closed:
            logger.warning(f"Attempted to send progress on closed stream {self.stream_id}")
            return False
            
        try:
            event = {
                "type": "progress",
                "stream_id": self.stream_id,
                "progress": progress,
                "total": total,
                "message": message,
                "timestamp": time.time(),
                "elapsed": time.time() - self.start_time
            }
            
            await self._send_event(event)
            return True
            
        except Exception as e:
            logger.error(f"Failed to send progress update: {e}")
            return False
    
    async def info(self, message: str, data: Optional[Dict] = None) -> bool:
        """Send an info message to the client.
        
        Args:
            message: Info message text
            data: Optional additional data
            
        Returns:
            True if message was sent successfully, False otherwise
        """
        if self.is_closed:
            logger.warning(f"Attempted to send info on closed stream {self.stream_id}")
            return False
            
        try:
            event = {
                "type": "info",
                "stream_id": self.stream_id,
                "message": message,
                "data": data,
                "timestamp": time.time(),
                "elapsed": time.time() - self.start_time
            }
            
            await self._send_event(event)
            return True
            
        except Exception as e:
            logger.error(f"Failed to send info message: {e}")
            return False
    
    async def warning(self, message: str, data: Optional[Dict] = None) -> bool:
        """Send a warning message to the client.
        
        Args:
            message: Warning message text
            data: Optional additional data
            
        Returns:
            True if message was sent successfully, False otherwise
        """
        if self.is_closed:
            return False
            
        try:
            event = {
                "type": "warning",
                "stream_id": self.stream_id,
                "message": message,
                "data": data,
                "timestamp": time.time(),
                "elapsed": time.time() - self.start_time
            }
            
            await self._send_event(event)
            return True
            
        except Exception as e:
            logger.error(f"Failed to send warning: {e}")
            return False
    
    async def error(self, message: str, error_data: Optional[Dict] = None) -> bool:
        """Send an error message to the client.
        
        Args:
            message: Error message text
            error_data: Optional error details
            
        Returns:
            True if message was sent successfully, False otherwise
        """
        if self.is_closed:
            return False
            
        try:
            event = {
                "type": "error",
                "stream_id": self.stream_id,
                "message": message,
                "error_data": error_data,
                "timestamp": time.time(),
                "elapsed": time.time() - self.start_time
            }
            
            await self._send_event(event)
            return True
            
        except Exception as e:
            logger.error(f"Failed to send error: {e}")
            return False
    
    async def send_data(self, data: Dict[str, Any]) -> bool:
        """Send arbitrary data to the client.
        
        Args:
            data: Data to send
            
        Returns:
            True if data was sent successfully, False otherwise
        """
        if self.is_closed:
            return False
            
        try:
            event = {
                "type": "data",
                "stream_id": self.stream_id,
                "data": data,
                "timestamp": time.time(),
                "elapsed": time.time() - self.start_time
            }
            
            await self._send_event(event)
            return True
            
        except Exception as e:
            logger.error(f"Failed to send data: {e}")
            return False
    
    async def _send_event(self, event: Dict[str, Any]) -> None:
        """Internal method to send an event to the client.
        
        Args:
            event: Event data to send
        """
        if self.is_closed:
            return
            
        try:
            message = json.dumps(event)
            await self.websocket.send(message)
            self.message_count += 1
            
            logger.debug(f"Sent event to stream {self.stream_id}: {event['type']}")
            
        except Exception as e:
            logger.error(f"Failed to send event to stream {self.stream_id}: {e}")
            self.is_closed = True
            raise
    
    async def close(self) -> None:
        """Close the streaming context and notify the client."""
        if self.is_closed:
            return
            
        try:
            final_event = {
                "type": "stream_closed",
                "stream_id": self.stream_id,
                "message_count": self.message_count,
                "total_elapsed": time.time() - self.start_time,
                "timestamp": time.time()
            }
            
            await self._send_event(final_event)
            self.is_closed = True
            
            logger.info(f"Closed stream {self.stream_id} after {self.message_count} messages")
            
        except Exception as e:
            logger.error(f"Error closing stream {self.stream_id}: {e}")
            self.is_closed = True
    
    def __str__(self) -> str:
        return f"StreamingContext(stream_id={self.stream_id}, messages={self.message_count}, closed={self.is_closed})"
    
    def __repr__(self) -> str:
        return self.__str__()
