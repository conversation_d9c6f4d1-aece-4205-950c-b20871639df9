#!/usr/bin/env python3
"""
Demo Client for Streaming MCP

This is an interactive client that demonstrates the streaming capabilities
of the Progressive MCP implementation.
"""

import asyncio
import logging
import sys
import time
from typing import Optional

from .streaming_client import StreamingMCPClient, StreamEvent, ToolResult

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DemoClient:
    """Interactive demo client for streaming MCP."""
    
    def __init__(self, server_url: str = "ws://localhost:8765"):
        """Initialize the demo client.
        
        Args:
            server_url: WebSocket URL of the streaming MCP server
        """
        self.server_url = server_url
        self.client = StreamingMCPClient(server_url)
        self.running = False
    
    async def start(self):
        """Start the demo client."""
        print("🚀 Streaming MCP Demo Client")
        print(f"📡 Connecting to server: {self.server_url}")
        
        # Connect to server
        if not await self.client.connect():
            print("❌ Failed to connect to server")
            return
        
        print("✅ Connected to server!")
        
        # Show available tools
        tools = self.client.get_tools()
        print(f"\n📋 Available tools ({len(tools)}):")
        for i, tool in enumerate(tools, 1):
            streaming = "🔄" if tool.get("supports_streaming") else "⚡"
            print(f"  {i}. {streaming} {tool['name']} - {tool.get('description', 'No description')}")
        
        print("\n" + "="*60)
        print("🎯 Demo Options:")
        print("1. Basic streaming demo")
        print("2. Long computation demo") 
        print("3. File processing demo")
        print("4. Network simulation demo")
        print("5. Custom tool call")
        print("6. List tools")
        print("0. Exit")
        print("="*60)
        
        self.running = True
        while self.running:
            try:
                choice = input("\n🎮 Select demo (0-6): ").strip()
                
                if choice == "0":
                    self.running = False
                elif choice == "1":
                    await self.demo_basic_streaming()
                elif choice == "2":
                    await self.demo_long_computation()
                elif choice == "3":
                    await self.demo_file_processing()
                elif choice == "4":
                    await self.demo_network_simulation()
                elif choice == "5":
                    await self.demo_custom_call()
                elif choice == "6":
                    await self.demo_list_tools()
                else:
                    print("❌ Invalid choice. Please select 0-6.")
                    
            except KeyboardInterrupt:
                print("\n🛑 Interrupted by user")
                self.running = False
            except Exception as e:
                print(f"❌ Error: {e}")
        
        # Disconnect
        await self.client.disconnect()
        print("👋 Goodbye!")
    
    async def demo_basic_streaming(self):
        """Demo basic streaming with process_items_streaming."""
        print("\n🔄 Basic Streaming Demo")
        print("This demo shows real-time progress updates while processing items.")
        
        phrase = input("Enter a phrase to process (or press Enter for 'streaming demo'): ").strip()
        if not phrase:
            phrase = "streaming demo"
        
        num_items = input("Number of items to process (default 8): ").strip()
        try:
            num_items = int(num_items) if num_items else 8
        except ValueError:
            num_items = 8
        
        print(f"\n🚀 Processing '{phrase}' with {num_items} items...")
        print("📊 Watch for real-time progress updates below:")
        print("-" * 50)
        
        # Progress callback
        def on_progress(event: StreamEvent):
            if event.type == "progress":
                bar_length = 30
                filled = int(bar_length * event.percentage / 100)
                bar = "█" * filled + "░" * (bar_length - filled)
                print(f"📊 [{bar}] {event.percentage:5.1f}% - {event.message}")
            elif event.type == "info":
                print(f"ℹ️  {event.message}")
            elif event.type == "warning":
                print(f"⚠️  {event.message}")
            elif event.type == "error":
                print(f"❌ {event.message}")
        
        # Call streaming tool
        start_time = time.time()
        result = await self.client.call_tool_streaming(
            "process_items_streaming",
            {"phrase": phrase, "num_items": num_items, "delay": 0.5},
            progress_callback=on_progress
        )
        end_time = time.time()
        
        print("-" * 50)
        if result.success:
            print(f"✅ Completed in {end_time - start_time:.2f} seconds")
            summary = result.result.get("summary", {})
            print(f"📈 Processed {summary.get('processed_count', 0)} items")
            print(f"⚡ Rate: {summary.get('items_per_second', 0):.2f} items/second")
        else:
            print(f"❌ Failed: {result.error}")
    
    async def demo_long_computation(self):
        """Demo long computation with streaming."""
        print("\n🧮 Long Computation Demo")
        print("This demo shows progress for computational tasks.")
        
        comp_type = input("Computation type (fibonacci/prime/factorial, default: fibonacci): ").strip()
        if comp_type not in ["fibonacci", "prime", "factorial"]:
            comp_type = "fibonacci"
        
        iterations = input("Number of iterations (default 15): ").strip()
        try:
            iterations = int(iterations) if iterations else 15
        except ValueError:
            iterations = 15
        
        print(f"\n🧮 Computing {comp_type} for {iterations} iterations...")
        print("📊 Progress updates:")
        print("-" * 50)
        
        def on_progress(event: StreamEvent):
            if event.type == "progress":
                print(f"📊 {event.progress:2d}/{event.total} ({event.percentage:5.1f}%) - {event.message}")
            elif event.type == "info":
                print(f"ℹ️  {event.message}")
        
        start_time = time.time()
        result = await self.client.call_tool_streaming(
            "long_computation_streaming",
            {"computation_type": comp_type, "iterations": iterations},
            progress_callback=on_progress
        )
        end_time = time.time()
        
        print("-" * 50)
        if result.success:
            print(f"✅ Computation completed in {end_time - start_time:.2f} seconds")
            summary = result.result.get("summary", {})
            print(f"🎯 First result: {summary.get('first_result')}")
            print(f"🎯 Last result: {summary.get('last_result')}")
        else:
            print(f"❌ Failed: {result.error}")
    
    async def demo_file_processing(self):
        """Demo file processing simulation."""
        print("\n📁 File Processing Demo")
        print("This demo simulates processing multiple files with detailed progress.")
        
        file_count = input("Number of files to process (default 5): ").strip()
        try:
            file_count = int(file_count) if file_count else 5
        except ValueError:
            file_count = 5
        
        mode = input("Processing mode (analyze/convert/compress, default: analyze): ").strip()
        if mode not in ["analyze", "convert", "compress"]:
            mode = "analyze"
        
        print(f"\n📁 Processing {file_count} files in '{mode}' mode...")
        print("📊 Progress updates:")
        print("-" * 60)
        
        def on_progress(event: StreamEvent):
            if event.type == "progress":
                print(f"📊 {event.progress:2d}/{event.total} ({event.percentage:5.1f}%) - {event.message}")
            elif event.type == "info":
                print(f"ℹ️  {event.message}")
            elif event.type == "warning":
                print(f"⚠️  {event.message}")
        
        start_time = time.time()
        result = await self.client.call_tool_streaming(
            "file_processing_simulation",
            {"file_count": file_count, "processing_mode": mode},
            progress_callback=on_progress
        )
        end_time = time.time()
        
        print("-" * 60)
        if result.success:
            print(f"✅ File processing completed in {end_time - start_time:.2f} seconds")
            summary = result.result.get("summary", {})
            print(f"📊 Files processed: {summary.get('files_processed', 0)}")
            print(f"📈 Processing rate: {summary.get('files_per_second', 0):.2f} files/second")
        else:
            print(f"❌ Failed: {result.error}")
    
    async def demo_network_simulation(self):
        """Demo network simulation."""
        print("\n🌐 Network Simulation Demo")
        print("This demo simulates checking multiple network endpoints.")
        
        endpoints = input("Number of endpoints to check (default 8): ").strip()
        try:
            endpoints = int(endpoints) if endpoints else 8
        except ValueError:
            endpoints = 8
        
        print(f"\n🌐 Checking {endpoints} network endpoints...")
        print("📊 Progress updates:")
        print("-" * 60)
        
        def on_progress(event: StreamEvent):
            if event.type == "progress":
                print(f"📊 {event.progress:2d}/{event.total} ({event.percentage:5.1f}%) - {event.message}")
            elif event.type == "info":
                print(f"ℹ️  {event.message}")
            elif event.type == "warning":
                print(f"⚠️  {event.message}")
        
        start_time = time.time()
        result = await self.client.call_tool_streaming(
            "network_simulation",
            {"endpoints": endpoints, "timeout": 2.0},
            progress_callback=on_progress
        )
        end_time = time.time()
        
        print("-" * 60)
        if result.success:
            print(f"✅ Network check completed in {end_time - start_time:.2f} seconds")
            summary = result.result.get("summary", {})
            print(f"📊 Success rate: {summary.get('success_rate', 0)}%")
            print(f"⚡ Average response time: {summary.get('average_response_time', 0)}ms")
        else:
            print(f"❌ Failed: {result.error}")
    
    async def demo_custom_call(self):
        """Demo custom tool call."""
        print("\n🛠️  Custom Tool Call")
        
        tools = self.client.get_tools()
        print("Available tools:")
        for i, tool in enumerate(tools, 1):
            streaming = "🔄" if tool.get("supports_streaming") else "⚡"
            print(f"  {i}. {streaming} {tool['name']}")
        
        tool_name = input("Enter tool name: ").strip()
        if not tool_name:
            print("❌ No tool name provided")
            return
        
        tool = self.client.get_tool(tool_name)
        if not tool:
            print(f"❌ Tool '{tool_name}' not found")
            return
        
        print(f"📋 Tool: {tool['name']}")
        print(f"📝 Description: {tool.get('description', 'No description')}")
        print(f"🔄 Supports streaming: {tool.get('supports_streaming', False)}")
        
        # Simple argument input (for demo purposes)
        args_input = input("Enter arguments as JSON (or press Enter for {}): ").strip()
        try:
            import json
            arguments = json.loads(args_input) if args_input else {}
        except json.JSONDecodeError:
            print("❌ Invalid JSON, using empty arguments")
            arguments = {}
        
        # Choose streaming or non-streaming
        use_streaming = tool.get("supports_streaming", False)
        if use_streaming:
            use_streaming = input("Use streaming? (y/n, default y): ").strip().lower() != "n"
        
        print(f"\n🚀 Calling {tool_name}{'(streaming)' if use_streaming else ''}...")
        
        if use_streaming:
            def on_progress(event: StreamEvent):
                if event.type == "progress":
                    print(f"📊 {event.progress}/{event.total} ({event.percentage:5.1f}%) - {event.message}")
                elif event.type == "info":
                    print(f"ℹ️  {event.message}")
                elif event.type == "warning":
                    print(f"⚠️  {event.message}")
            
            result = await self.client.call_tool_streaming(tool_name, arguments, on_progress)
        else:
            result = await self.client.call_tool(tool_name, arguments)
        
        if result.success:
            print("✅ Tool call successful!")
            print(f"📄 Result: {result.result}")
        else:
            print(f"❌ Tool call failed: {result.error}")
    
    async def demo_list_tools(self):
        """List all available tools with details."""
        print("\n📋 Available Tools")
        
        tools = self.client.get_tools()
        for i, tool in enumerate(tools, 1):
            streaming = "🔄 Streaming" if tool.get("supports_streaming") else "⚡ Standard"
            print(f"\n{i}. {tool['name']} ({streaming})")
            print(f"   📝 {tool.get('description', 'No description')}")
            
            params = tool.get("parameters", {}).get("properties", {})
            if params:
                print("   📋 Parameters:")
                for param_name, param_info in params.items():
                    param_type = param_info.get("type", "unknown")
                    print(f"      • {param_name}: {param_type}")


async def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Streaming MCP Demo Client")
    parser.add_argument("--server", default="ws://localhost:8765", help="Server WebSocket URL")
    args = parser.parse_args()
    
    client = DemoClient(args.server)
    await client.start()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
