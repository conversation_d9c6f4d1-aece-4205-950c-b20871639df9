#!/usr/bin/env python3
"""
Streaming MCP Client

A WebSocket-based client for connecting to streaming MCP servers.
Supports both standard tool calls and streaming tool calls with
real-time progress updates.
"""

import asyncio
import json
import logging
import time
import uuid
from typing import Dict, Any, Callable, Optional, List, AsyncGenerator
import websockets
from websockets.client import WebSocketClientProtocol

logger = logging.getLogger(__name__)


class ToolResult:
    """Result from a tool execution."""
    
    def __init__(self, success: bool, result: Any = None, error: str = None, stream_id: str = None):
        self.success = success
        self.result = result
        self.error = error
        self.stream_id = stream_id
        self.timestamp = time.time()


class StreamEvent:
    """Event received during streaming tool execution."""
    
    def __init__(self, event_type: str, data: Dict[str, Any]):
        self.type = event_type
        self.data = data
        self.timestamp = time.time()
        
        # Extract common fields
        self.stream_id = data.get("stream_id")
        self.message = data.get("message", "")
        
        # Progress-specific fields
        if event_type == "progress":
            self.progress = data.get("progress", 0)
            self.total = data.get("total", 1)
            self.percentage = (self.progress / self.total * 100) if self.total > 0 else 0
        else:
            self.progress = None
            self.total = None
            self.percentage = None


class StreamingMCPClient:
    """WebSocket-based client for streaming MCP servers.
    
    This client can connect to streaming MCP servers and execute both
    standard and streaming tool calls with real-time progress updates.
    """
    
    def __init__(self, server_url: str, timeout: float = 30.0):
        """Initialize the streaming MCP client.
        
        Args:
            server_url: WebSocket URL of the streaming MCP server
            timeout: Default timeout for operations
        """
        self.server_url = server_url
        self.timeout = timeout
        self.websocket: Optional[WebSocketClientProtocol] = None
        self.connected = False
        self.tools: List[Dict[str, Any]] = []
        self.pending_requests: Dict[str, asyncio.Future] = {}
        self.active_streams: Dict[str, Dict[str, Any]] = {}
        
    async def connect(self) -> bool:
        """Connect to the streaming MCP server.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            logger.info(f"Connecting to streaming MCP server at {self.server_url}")
            
            self.websocket = await websockets.connect(
                self.server_url,
                ping_interval=20,
                ping_timeout=10
            )
            
            self.connected = True
            
            # Start message handler
            asyncio.create_task(self._message_handler())
            
            # Get available tools
            await self._list_tools()
            
            logger.info(f"Connected to server with {len(self.tools)} tools")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to server: {e}")
            self.connected = False
            return False
    
    async def disconnect(self):
        """Disconnect from the server."""
        if self.websocket and self.connected:
            # Close any active streams
            for stream_id in list(self.active_streams.keys()):
                await self._close_stream(stream_id)
            
            await self.websocket.close()
            self.connected = False
            logger.info("Disconnected from server")
    
    async def _message_handler(self):
        """Handle incoming messages from the server."""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self._handle_message(data)
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON received: {e}")
                except Exception as e:
                    logger.error(f"Error handling message: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info("Connection to server closed")
            self.connected = False
        except Exception as e:
            logger.error(f"Message handler error: {e}")
            self.connected = False
    
    async def _handle_message(self, data: Dict[str, Any]):
        """Handle a message from the server.
        
        Args:
            data: Message data
        """
        # Handle responses to requests
        if "id" in data:
            request_id = data["id"]
            if request_id in self.pending_requests:
                future = self.pending_requests[request_id]
                if "error" in data:
                    future.set_exception(Exception(data["error"]["message"]))
                else:
                    future.set_result(data)
                del self.pending_requests[request_id]
                return
        
        # Handle streaming events
        if "stream_id" in data:
            stream_id = data["stream_id"]
            if stream_id in self.active_streams:
                stream_info = self.active_streams[stream_id]
                callback = stream_info.get("callback")
                
                if data.get("type") == "stream_started":
                    # Stream started notification
                    pass
                elif "type" in data:
                    # Stream event
                    event = StreamEvent(data["type"], data)
                    if callback:
                        try:
                            await callback(event)
                        except Exception as e:
                            logger.error(f"Error in stream callback: {e}")
                
                # Handle stream closure
                if data.get("type") == "stream_closed":
                    if stream_id in self.active_streams:
                        del self.active_streams[stream_id]
    
    async def _send_request(self, method: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Send a request to the server and wait for response.
        
        Args:
            method: Request method
            params: Request parameters
            
        Returns:
            Response data
        """
        if not self.connected:
            raise Exception("Not connected to server")
        
        request_id = str(uuid.uuid4())
        request = {
            "id": request_id,
            "method": method,
            "params": params or {}
        }
        
        # Create future for response
        future = asyncio.Future()
        self.pending_requests[request_id] = future
        
        try:
            # Send request
            await self.websocket.send(json.dumps(request))
            
            # Wait for response
            response = await asyncio.wait_for(future, timeout=self.timeout)
            return response
            
        except asyncio.TimeoutError:
            if request_id in self.pending_requests:
                del self.pending_requests[request_id]
            raise Exception(f"Request timeout after {self.timeout} seconds")
        except Exception as e:
            if request_id in self.pending_requests:
                del self.pending_requests[request_id]
            raise
    
    async def _list_tools(self):
        """Get the list of available tools from the server."""
        try:
            response = await self._send_request("list_tools")
            self.tools = response.get("result", {}).get("tools", [])
            logger.info(f"Retrieved {len(self.tools)} tools from server")
        except Exception as e:
            logger.error(f"Failed to list tools: {e}")
            self.tools = []
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> ToolResult:
        """Call a tool on the server (non-streaming).
        
        Args:
            tool_name: Name of the tool to call
            arguments: Tool arguments
            
        Returns:
            ToolResult object
        """
        try:
            response = await self._send_request("call_tool", {
                "name": tool_name,
                "arguments": arguments or {}
            })
            
            return ToolResult(
                success=True,
                result=response.get("result")
            )
            
        except Exception as e:
            logger.error(f"Tool call failed: {e}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def call_tool_streaming(
        self,
        tool_name: str,
        arguments: Dict[str, Any] = None,
        progress_callback: Optional[Callable[[StreamEvent], None]] = None
    ) -> ToolResult:
        """Call a tool with streaming support.
        
        Args:
            tool_name: Name of the tool to call
            arguments: Tool arguments
            progress_callback: Callback for progress events
            
        Returns:
            ToolResult object
        """
        try:
            # Register stream callback
            stream_info = {"callback": progress_callback}
            
            response = await self._send_request("call_tool_streaming", {
                "name": tool_name,
                "arguments": arguments or {}
            })
            
            # Track the stream
            stream_id = response.get("stream_id")
            if stream_id:
                self.active_streams[stream_id] = stream_info
            
            return ToolResult(
                success=True,
                result=response.get("result"),
                stream_id=stream_id
            )
            
        except Exception as e:
            logger.error(f"Streaming tool call failed: {e}")
            return ToolResult(
                success=False,
                error=str(e)
            )
    
    async def _close_stream(self, stream_id: str):
        """Close a stream.
        
        Args:
            stream_id: Stream identifier
        """
        try:
            await self._send_request("close_stream", {"stream_id": stream_id})
        except Exception as e:
            logger.error(f"Failed to close stream {stream_id}: {e}")
        
        if stream_id in self.active_streams:
            del self.active_streams[stream_id]
    
    def get_tools(self) -> List[Dict[str, Any]]:
        """Get the list of available tools.
        
        Returns:
            List of tool schemas
        """
        return self.tools.copy()
    
    def get_tool(self, name: str) -> Optional[Dict[str, Any]]:
        """Get a specific tool schema.
        
        Args:
            name: Tool name
            
        Returns:
            Tool schema or None if not found
        """
        for tool in self.tools:
            if tool.get("name") == name:
                return tool
        return None
    
    def is_connected(self) -> bool:
        """Check if connected to server.
        
        Returns:
            True if connected, False otherwise
        """
        return self.connected and self.websocket and not self.websocket.closed
