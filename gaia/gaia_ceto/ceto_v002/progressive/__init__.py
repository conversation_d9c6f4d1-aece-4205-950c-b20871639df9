# Progressive MCP - Streaming MCP Implementation
# 
# This package provides a streaming-capable MCP (Model Context Protocol) implementation
# that allows real-time progress updates from server to client.
#
# Components:
# - streaming_server.py: WebSocket-based MCP server with streaming support
# - streaming_client.py: Client library for connecting to streaming MCP servers
# - demo_tools.py: Example tools that demonstrate streaming progress
# - demo_client.py: Example client application
# - README.md: Documentation and usage examples

__version__ = "1.0.0"
__author__ = "GAIA CETO Team"
__description__ = "Streaming MCP Implementation with Real-time Progress Updates"
