#!/usr/bin/env python3
"""
Demo Tools for Streaming MCP

This module contains example tools that demonstrate the streaming
capabilities of the Progressive MCP implementation.
"""

import asyncio
import time
import random
import json
from typing import List, Dict, Any, Optional

from .streaming_context import StreamingContext


async def process_items_streaming(
    phrase: str,
    num_items: int = 10,
    delay: float = 0.5,
    ctx: StreamingContext = None
) -> Dict[str, Any]:
    """Process a list of items with real-time progress streaming.
    
    This tool demonstrates basic streaming progress updates by processing
    a list of items derived from the input phrase.
    
    Args:
        phrase: Base phrase to create items from
        num_items: Number of items to process (default: 10)
        delay: Delay between items in seconds (default: 0.5)
        ctx: Streaming context for progress updates
        
    Returns:
        Dictionary containing processing results
    """
    start_time = time.time()
    
    # Generate items to process
    items = []
    for i in range(num_items):
        if i % 4 == 0:
            items.append(f"{phrase}_{i}")
        elif i % 4 == 1:
            items.append(f"{phrase}_variant_{i}")
        elif i % 4 == 2:
            items.append(f"processed_{phrase}_{i}")
        else:
            items.append(f"{phrase}_final_{i}")
    
    results = []
    
    # Initial progress report
    if ctx:
        await ctx.report_progress(0, len(items), f"Starting to process {len(items)} items based on '{phrase}'")
        await ctx.info(f"🚀 Processing pipeline initialized with {len(items)} items")
    
    # Process each item
    for i, item in enumerate(items):
        if ctx:
            await ctx.report_progress(i, len(items), f"Processing item {i+1}/{len(items)}: {item}")
        
        # Simulate processing work
        await asyncio.sleep(delay)
        
        # Process the item
        processed_item = {
            "original": item,
            "processed": item.upper(),
            "timestamp": time.strftime('%H:%M:%S'),
            "index": i,
            "based_on_phrase": phrase
        }
        results.append(processed_item)
        
        if ctx:
            await ctx.info(f"✅ Completed: {item} → {item.upper()}")
    
    # Final progress report
    if ctx:
        await ctx.report_progress(len(items), len(items), "All items processed successfully!")
        await ctx.info(f"🎉 Processing complete: {len(items)} items in {time.time() - start_time:.2f} seconds")
    
    # Return comprehensive results
    return {
        "type": "processing_results",
        "title": f"Processing Results for: {phrase}",
        "summary": {
            "base_phrase": phrase,
            "total_items": len(items),
            "processed_count": len(results),
            "duration_seconds": round(time.time() - start_time, 2),
            "items_per_second": round(len(results) / (time.time() - start_time), 2)
        },
        "results": results,
        "timestamp": time.strftime('%H:%M:%S')
    }


async def long_computation_streaming(
    computation_type: str = "fibonacci",
    iterations: int = 20,
    ctx: StreamingContext = None
) -> Dict[str, Any]:
    """Perform a long computation with progress streaming.
    
    This tool demonstrates streaming progress for computational tasks.
    
    Args:
        computation_type: Type of computation ("fibonacci", "prime", "factorial")
        iterations: Number of iterations to perform
        ctx: Streaming context for progress updates
        
    Returns:
        Dictionary containing computation results
    """
    start_time = time.time()
    results = []
    
    if ctx:
        await ctx.report_progress(0, iterations, f"Starting {computation_type} computation for {iterations} iterations")
        await ctx.info(f"🧮 Initializing {computation_type} computation engine")
    
    for i in range(iterations):
        if ctx:
            await ctx.report_progress(i, iterations, f"Computing {computation_type} iteration {i+1}")
        
        # Perform computation based on type
        if computation_type == "fibonacci":
            if i <= 1:
                result = i
            else:
                # Calculate fibonacci number (inefficiently for demo purposes)
                a, b = 0, 1
                for _ in range(i):
                    a, b = b, a + b
                result = a
        elif computation_type == "prime":
            # Find the i-th prime number
            count = 0
            num = 2
            while count <= i:
                is_prime = True
                for j in range(2, int(num ** 0.5) + 1):
                    if num % j == 0:
                        is_prime = False
                        break
                if is_prime:
                    if count == i:
                        result = num
                        break
                    count += 1
                num += 1
        elif computation_type == "factorial":
            result = 1
            for j in range(1, i + 2):
                result *= j
        else:
            result = i ** 2  # Default to square
        
        results.append({
            "iteration": i,
            "input": i,
            "result": result,
            "computation_type": computation_type
        })
        
        if ctx:
            await ctx.info(f"📊 {computation_type}({i}) = {result}")
        
        # Add some delay to make progress visible
        await asyncio.sleep(0.3)
    
    if ctx:
        await ctx.report_progress(iterations, iterations, f"Computation complete!")
        await ctx.info(f"🎯 All {iterations} {computation_type} computations completed")
    
    return {
        "type": "computation_results",
        "computation_type": computation_type,
        "iterations": iterations,
        "duration_seconds": round(time.time() - start_time, 2),
        "results": results,
        "summary": {
            "first_result": results[0]["result"] if results else None,
            "last_result": results[-1]["result"] if results else None,
            "total_computed": len(results)
        }
    }


async def file_processing_simulation(
    file_count: int = 5,
    processing_mode: str = "analyze",
    ctx: StreamingContext = None
) -> Dict[str, Any]:
    """Simulate file processing with detailed progress streaming.
    
    This tool simulates processing multiple files with different stages
    and detailed progress reporting.
    
    Args:
        file_count: Number of files to process
        processing_mode: Processing mode ("analyze", "convert", "compress")
        ctx: Streaming context for progress updates
        
    Returns:
        Dictionary containing processing results
    """
    start_time = time.time()
    
    # Generate fake file list
    files = [
        f"document_{i:03d}.{random.choice(['txt', 'pdf', 'docx', 'csv'])}"
        for i in range(file_count)
    ]
    
    processed_files = []
    total_steps = file_count * 3  # 3 steps per file
    current_step = 0
    
    if ctx:
        await ctx.report_progress(0, total_steps, f"Initializing {processing_mode} for {file_count} files")
        await ctx.info(f"📁 File processing pipeline started")
        await ctx.info(f"🔧 Mode: {processing_mode}")
        await ctx.info(f"📊 Files to process: {file_count}")
    
    for i, filename in enumerate(files):
        file_size = random.randint(1024, 1024*1024)  # Random file size
        
        # Step 1: Reading file
        current_step += 1
        if ctx:
            await ctx.report_progress(current_step, total_steps, f"Reading {filename}")
            await ctx.info(f"📖 Reading file: {filename} ({file_size:,} bytes)")
        await asyncio.sleep(0.4)
        
        # Step 2: Processing file
        current_step += 1
        if ctx:
            await ctx.report_progress(current_step, total_steps, f"Processing {filename}")
            await ctx.info(f"⚙️  {processing_mode.title()}ing: {filename}")
        await asyncio.sleep(0.6)
        
        # Step 3: Saving results
        current_step += 1
        output_filename = f"{processing_mode}_{filename}"
        if ctx:
            await ctx.report_progress(current_step, total_steps, f"Saving {output_filename}")
            await ctx.info(f"💾 Saving: {output_filename}")
        await asyncio.sleep(0.3)
        
        # Record results
        processed_files.append({
            "input_file": filename,
            "output_file": output_filename,
            "input_size": file_size,
            "output_size": int(file_size * random.uniform(0.7, 1.3)),  # Simulated size change
            "processing_time": 1.3,  # Total time for this file
            "status": "completed"
        })
        
        if ctx:
            await ctx.info(f"✅ Completed: {filename} → {output_filename}")
    
    if ctx:
        await ctx.report_progress(total_steps, total_steps, "All files processed!")
        await ctx.info(f"🎉 File processing complete: {file_count} files processed")
    
    total_input_size = sum(f["input_size"] for f in processed_files)
    total_output_size = sum(f["output_size"] for f in processed_files)
    
    return {
        "type": "file_processing_results",
        "processing_mode": processing_mode,
        "summary": {
            "files_processed": len(processed_files),
            "total_input_size": total_input_size,
            "total_output_size": total_output_size,
            "compression_ratio": round(total_output_size / total_input_size, 2) if total_input_size > 0 else 1.0,
            "duration_seconds": round(time.time() - start_time, 2),
            "files_per_second": round(len(processed_files) / (time.time() - start_time), 2)
        },
        "files": processed_files,
        "timestamp": time.strftime('%H:%M:%S')
    }


async def network_simulation(
    endpoints: int = 8,
    timeout: float = 2.0,
    ctx: StreamingContext = None
) -> Dict[str, Any]:
    """Simulate network operations with progress streaming.
    
    This tool simulates checking multiple network endpoints with
    realistic timing and occasional failures.
    
    Args:
        endpoints: Number of endpoints to check
        timeout: Timeout for each endpoint check
        ctx: Streaming context for progress updates
        
    Returns:
        Dictionary containing network check results
    """
    start_time = time.time()
    
    # Generate fake endpoints
    endpoint_list = [
        f"https://api-{i:02d}.example.com/health"
        for i in range(endpoints)
    ]
    
    results = []
    
    if ctx:
        await ctx.report_progress(0, endpoints, f"Starting network checks for {endpoints} endpoints")
        await ctx.info(f"🌐 Network connectivity test initiated")
        await ctx.info(f"⏱️  Timeout: {timeout} seconds per endpoint")
    
    for i, endpoint in enumerate(endpoint_list):
        if ctx:
            await ctx.report_progress(i, endpoints, f"Checking {endpoint}")
            await ctx.info(f"🔍 Pinging: {endpoint}")
        
        # Simulate network request
        request_start = time.time()
        await asyncio.sleep(random.uniform(0.1, min(timeout, 1.5)))
        request_time = time.time() - request_start
        
        # Simulate occasional failures
        success = random.random() > 0.15  # 85% success rate
        status_code = 200 if success else random.choice([404, 500, 503, 408])
        
        result = {
            "endpoint": endpoint,
            "success": success,
            "status_code": status_code,
            "response_time": round(request_time * 1000, 2),  # Convert to milliseconds
            "timestamp": time.strftime('%H:%M:%S')
        }
        results.append(result)
        
        if ctx:
            if success:
                await ctx.info(f"✅ {endpoint}: {status_code} ({result['response_time']}ms)")
            else:
                await ctx.warning(f"❌ {endpoint}: {status_code} ({result['response_time']}ms)")
    
    if ctx:
        await ctx.report_progress(endpoints, endpoints, "Network checks complete!")
        successful = sum(1 for r in results if r["success"])
        await ctx.info(f"📊 Results: {successful}/{endpoints} endpoints responding")
    
    successful_results = [r for r in results if r["success"]]
    avg_response_time = sum(r["response_time"] for r in successful_results) / len(successful_results) if successful_results else 0
    
    return {
        "type": "network_check_results",
        "summary": {
            "total_endpoints": endpoints,
            "successful": len(successful_results),
            "failed": endpoints - len(successful_results),
            "success_rate": round(len(successful_results) / endpoints * 100, 1),
            "average_response_time": round(avg_response_time, 2),
            "duration_seconds": round(time.time() - start_time, 2)
        },
        "results": results,
        "timestamp": time.strftime('%H:%M:%S')
    }
