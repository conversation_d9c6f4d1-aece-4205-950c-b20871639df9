#!/usr/bin/env python3
"""
Streaming MCP Server

A WebSocket-based MCP server that supports real-time progress streaming
from tools to clients. This server extends the standard MCP protocol
to enable bidirectional communication and live progress updates.
"""

import asyncio
import json
import logging
import time
import uuid
from typing import Dict, Any, Callable, Optional, List
import websockets
from websockets.server import WebSocketServerProtocol
import inspect

from .streaming_context import StreamingContext

logger = logging.getLogger(__name__)


class StreamingMCPServer:
    """WebSocket-based MCP server with streaming capabilities.

    This server supports both standard MCP tool calls and streaming tool calls
    that can send real-time progress updates to clients.
    """

    def __init__(self, name: str = "StreamingMCPServer"):
        """Initialize the streaming MCP server.

        Args:
            name: Server name for identification
        """
        self.name = name
        self.tools: Dict[str, Callable] = {}
        self.tool_schemas: Dict[str, Dict] = {}
        self.active_streams: Dict[str, StreamingContext] = {}
        self.connected_clients: Dict[str, WebSocketServerProtocol] = {}
        self.server = None

    def tool(self, name: Optional[str] = None, description: Optional[str] = None):
        """Decorator to register a tool with the server.

        Args:
            name: Tool name (defaults to function name)
            description: Tool description (defaults to function docstring)
        """
        def decorator(func: Callable) -> Callable:
            tool_name = name or func.__name__
            tool_description = description or func.__doc__ or f"Tool: {tool_name}"

            # Analyze function signature to create schema
            sig = inspect.signature(func)
            schema = {
                "name": tool_name,
                "description": tool_description,
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }

            # Check if function supports streaming (has StreamingContext parameter)
            supports_streaming = False
            for param_name, param in sig.parameters.items():
                if param.annotation == StreamingContext or param_name == "ctx":
                    supports_streaming = True
                    continue

                # Add parameter to schema
                if param.default == inspect.Parameter.empty:
                    schema["parameters"]["required"].append(param_name)

                # Basic type inference
                param_type = "string"  # Default
                if param.annotation == int:
                    param_type = "integer"
                elif param.annotation == float:
                    param_type = "number"
                elif param.annotation == bool:
                    param_type = "boolean"
                elif param.annotation == list:
                    param_type = "array"
                elif param.annotation == dict:
                    param_type = "object"

                schema["parameters"]["properties"][param_name] = {
                    "type": param_type,
                    "description": f"Parameter: {param_name}"
                }

            schema["supports_streaming"] = supports_streaming

            self.tools[tool_name] = func
            self.tool_schemas[tool_name] = schema

            logger.info(f"Registered tool: {tool_name} (streaming: {supports_streaming})")
            return func

        return decorator

    async def start_server(self, host: str = "localhost", port: int = 8765):
        """Start the WebSocket server.

        Args:
            host: Host to bind to
            port: Port to bind to
        """
        logger.info(f"Starting {self.name} on {host}:{port}")

        self.server = await websockets.serve(
            self.handle_client,
            host,
            port,
            ping_interval=20,
            ping_timeout=10
        )

        logger.info(f"Server started on ws://{host}:{port}")
        logger.info(f"Registered tools: {list(self.tools.keys())}")

    async def handle_client(self, websocket: WebSocketServerProtocol, path: str):
        """Handle a new client connection.

        Args:
            websocket: WebSocket connection
            path: Connection path
        """
        client_id = str(uuid.uuid4())
        self.connected_clients[client_id] = websocket

        logger.info(f"Client {client_id} connected from {websocket.remote_address}")

        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(client_id, websocket, data)
                except json.JSONDecodeError as e:
                    await self.send_error(websocket, f"Invalid JSON: {e}")
                except Exception as e:
                    logger.error(f"Error handling message from {client_id}: {e}")
                    await self.send_error(websocket, f"Internal error: {e}")

        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client {client_id} disconnected")
        except Exception as e:
            logger.error(f"Error with client {client_id}: {e}")
        finally:
            # Clean up client
            if client_id in self.connected_clients:
                del self.connected_clients[client_id]

            # Close any active streams for this client
            streams_to_close = [
                stream_id for stream_id, ctx in self.active_streams.items()
                if ctx.websocket == websocket
            ]
            for stream_id in streams_to_close:
                await self.close_stream(stream_id)

    async def handle_message(self, client_id: str, websocket: WebSocketServerProtocol, data: Dict[str, Any]):
        """Handle a message from a client.

        Args:
            client_id: Client identifier
            websocket: WebSocket connection
            data: Message data
        """
        method = data.get("method")

        if method == "list_tools":
            await self.handle_list_tools(websocket, data)
        elif method == "call_tool":
            await self.handle_call_tool(websocket, data, streaming=False)
        elif method == "call_tool_streaming":
            await self.handle_call_tool(websocket, data, streaming=True)
        elif method == "close_stream":
            await self.handle_close_stream(websocket, data)
        else:
            await self.send_error(websocket, f"Unknown method: {method}")

    async def handle_list_tools(self, websocket: WebSocketServerProtocol, data: Dict[str, Any]):
        """Handle a list_tools request.

        Args:
            websocket: WebSocket connection
            data: Request data
        """
        response = {
            "id": data.get("id"),
            "result": {
                "tools": list(self.tool_schemas.values())
            }
        }
        await websocket.send(json.dumps(response))

    async def handle_call_tool(self, websocket: WebSocketServerProtocol, data: Dict[str, Any], streaming: bool = False):
        """Handle a tool call request.

        Args:
            websocket: WebSocket connection
            data: Request data
            streaming: Whether this is a streaming call
        """
        request_id = data.get("id")
        params = data.get("params", {})
        tool_name = params.get("name")
        arguments = params.get("arguments", {})

        if tool_name not in self.tools:
            await self.send_error(websocket, f"Unknown tool: {tool_name}", request_id)
            return

        tool_func = self.tools[tool_name]
        tool_schema = self.tool_schemas[tool_name]

        # Check if streaming is requested but not supported
        if streaming and not tool_schema.get("supports_streaming", False):
            await self.send_error(websocket, f"Tool {tool_name} does not support streaming", request_id)
            return

        try:
            # Create streaming context if needed
            ctx = None
            if streaming and tool_schema.get("supports_streaming", False):
                stream_id = str(uuid.uuid4())
                ctx = StreamingContext(stream_id, websocket)
                self.active_streams[stream_id] = ctx

                # Notify client that streaming has started
                await websocket.send(json.dumps({
                    "id": request_id,
                    "type": "stream_started",
                    "stream_id": stream_id
                }))

            # Call the tool
            sig = inspect.signature(tool_func)
            call_args = arguments.copy()

            # Inject streaming context if the tool expects it
            for param_name, param in sig.parameters.items():
                if param.annotation == StreamingContext or param_name == "ctx":
                    call_args[param_name] = ctx
                    break

            # Execute the tool
            if asyncio.iscoroutinefunction(tool_func):
                result = await tool_func(**call_args)
            else:
                result = tool_func(**call_args)

            # Send the final result
            response = {
                "id": request_id,
                "result": result
            }

            if streaming and ctx:
                response["stream_id"] = ctx.stream_id
                await ctx.close()
                if ctx.stream_id in self.active_streams:
                    del self.active_streams[ctx.stream_id]

            await websocket.send(json.dumps(response))

        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            await self.send_error(websocket, f"Tool execution error: {e}", request_id)

            # Clean up stream if it was created
            if streaming and ctx and ctx.stream_id in self.active_streams:
                await ctx.close()
                del self.active_streams[ctx.stream_id]

    async def handle_close_stream(self, websocket: WebSocketServerProtocol, data: Dict[str, Any]):
        """Handle a close_stream request.

        Args:
            websocket: WebSocket connection
            data: Request data
        """
        stream_id = data.get("stream_id")
        if stream_id and stream_id in self.active_streams:
            await self.close_stream(stream_id)

    async def close_stream(self, stream_id: str):
        """Close an active stream.

        Args:
            stream_id: Stream identifier
        """
        if stream_id in self.active_streams:
            ctx = self.active_streams[stream_id]
            await ctx.close()
            del self.active_streams[stream_id]
            logger.info(f"Closed stream {stream_id}")

    async def send_error(self, websocket: WebSocketServerProtocol, error_message: str, request_id: Optional[str] = None):
        """Send an error response to a client.

        Args:
            websocket: WebSocket connection
            error_message: Error message
            request_id: Optional request ID
        """
        response = {
            "error": {
                "message": error_message,
                "timestamp": time.time()
            }
        }

        if request_id:
            response["id"] = request_id

        await websocket.send(json.dumps(response))

    async def stop_server(self):
        """Stop the server and clean up resources."""
        if self.server:
            # Close all active streams
            for stream_id in list(self.active_streams.keys()):
                await self.close_stream(stream_id)

            # Close server
            self.server.close()
            await self.server.wait_closed()
            logger.info("Server stopped")

    def get_stats(self) -> Dict[str, Any]:
        """Get server statistics.

        Returns:
            Dictionary containing server stats
        """
        return {
            "name": self.name,
            "tools": len(self.tools),
            "connected_clients": len(self.connected_clients),
            "active_streams": len(self.active_streams),
            "tool_names": list(self.tools.keys())
        }


# Example usage
if __name__ == "__main__":
    import sys
    import argparse

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    async def main():
        parser = argparse.ArgumentParser(description="Streaming MCP Server")
        parser.add_argument("--host", default="localhost", help="Host to bind to")
        parser.add_argument("--port", type=int, default=8765, help="Port to bind to")
        args = parser.parse_args()

        server = StreamingMCPServer("ExampleStreamingServer")

        # Register example tools (will be defined in demo_tools.py)
        @server.tool(description="Simple echo tool")
        def echo(message: str) -> str:
            return f"Echo: {message}"

        try:
            await server.start_server(args.host, args.port)
            print(f"Server running on ws://{args.host}:{args.port}")
            print("Press Ctrl+C to stop")

            # Keep server running
            await asyncio.Future()  # Run forever

        except KeyboardInterrupt:
            print("\nShutting down server...")
            await server.stop_server()

    asyncio.run(main())
