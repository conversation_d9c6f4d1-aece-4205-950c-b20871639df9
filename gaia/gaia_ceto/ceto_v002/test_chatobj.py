"""
Tests for the chatobj module.

These tests focus on the core functionality of the chatobj module,
using only the MockLLM to avoid making API calls to external providers.
"""

import os
import json
import tempfile
import unittest
from datetime import datetime
from unittest.mock import patch, MagicMock

from gaia.gaia_ceto.ceto_v002.chatobj import (
    Conversation,
    ChatManager,
    MockLLM,
    LLMInterface
)


class TestMockLLM(unittest.TestCase):
    """Test the MockLLM class."""

    def test_default_response(self):
        """Test that the default response function works correctly."""
        llm = MockLLM()
        prompt = "Hello, world!"
        context = []
        response = llm.generate_response(prompt, context)
        self.assertEqual(response, f"Mock LLM received: {prompt}")

    def test_custom_response_function(self):
        """Test that a custom response function works correctly."""
        def custom_response(prompt, context, kwargs):
            return f"Custom response to: {prompt}"

        llm = MockLLM(response_func=custom_response)
        prompt = "Hello, world!"
        context = []
        response = llm.generate_response(prompt, context)
        self.assertEqual(response, f"Custom response to: {prompt}")

    def test_with_context(self):
        """Test that the MockLLM correctly handles context."""
        llm = MockLLM()
        prompt = "What's the weather like?"
        context = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello!"},
            {"role": "assistant", "content": "Hi there! How can I help you today?"}
        ]
        response = llm.generate_response(prompt, context)
        self.assertEqual(response, f"Mock LLM received: {prompt}")


class TestConversation(unittest.TestCase):
    """Test the Conversation class."""

    def test_initialization(self):
        """Test that a conversation is initialized correctly."""
        conversation = Conversation(
            conversation_id="test-id",
            user_id="test-user",
            title="Test Conversation",
            metadata={"key": "value"}
        )

        self.assertEqual(conversation.conversation_id, "test-id")
        self.assertEqual(conversation.user_id, "test-user")
        self.assertEqual(conversation.title, "Test Conversation")
        self.assertEqual(conversation.metadata, {"key": "value"})
        self.assertEqual(conversation.messages, [])

    def test_auto_id_generation(self):
        """Test that a conversation ID is auto-generated if not provided."""
        conversation = Conversation()
        self.assertIsNotNone(conversation.conversation_id)
        self.assertGreater(len(conversation.conversation_id), 0)

    def test_add_message(self):
        """Test adding a message to a conversation."""
        conversation = Conversation()
        conversation.add_message("user", "Hello!")

        self.assertEqual(len(conversation.messages), 1)
        self.assertEqual(conversation.messages[0]["role"], "user")
        self.assertEqual(conversation.messages[0]["content"], "Hello!")
        self.assertIn("timestamp", conversation.messages[0])

    def test_to_dict(self):
        """Test converting a conversation to a dictionary."""
        conversation = Conversation(
            conversation_id="test-id",
            user_id="test-user",
            title="Test Conversation"
        )
        conversation.add_message("user", "Hello!")

        data = conversation.to_dict()
        self.assertEqual(data["conversation_id"], "test-id")
        self.assertEqual(data["user_id"], "test-user")
        self.assertEqual(data["title"], "Test Conversation")
        self.assertEqual(len(data["messages"]), 1)
        self.assertEqual(data["messages"][0]["role"], "user")
        self.assertEqual(data["messages"][0]["content"], "Hello!")

    def test_from_dict(self):
        """Test creating a conversation from a dictionary."""
        data = {
            "conversation_id": "test-id",
            "user_id": "test-user",
            "title": "Test Conversation",
            "metadata": {"key": "value"},
            "messages": [
                {
                    "role": "user",
                    "content": "Hello!",
                    "timestamp": datetime.now().isoformat()
                }
            ],
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }

        conversation = Conversation.from_dict(data)
        self.assertEqual(conversation.conversation_id, "test-id")
        self.assertEqual(conversation.user_id, "test-user")
        self.assertEqual(conversation.title, "Test Conversation")
        self.assertEqual(conversation.metadata, {"key": "value"})
        self.assertEqual(len(conversation.messages), 1)
        self.assertEqual(conversation.messages[0]["role"], "user")
        self.assertEqual(conversation.messages[0]["content"], "Hello!")


class TestChatManager(unittest.TestCase):
    """Test the ChatManager class."""

    def setUp(self):
        """Set up a temporary directory for storing conversations."""
        self.temp_dir = tempfile.mkdtemp()
        self.chat_manager = ChatManager(storage_dir=self.temp_dir)

    def tearDown(self):
        """Clean up the temporary directory."""
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_create_conversation(self):
        """Test creating a new conversation."""
        conversation = self.chat_manager.create_conversation(
            user_id="test-user",
            title="Test Conversation"
        )

        self.assertEqual(conversation.user_id, "test-user")
        self.assertEqual(conversation.title, "Test Conversation")
        self.assertEqual(self.chat_manager.active_conversation, conversation)

    def test_add_message(self):
        """Test adding a message to the active conversation."""
        self.chat_manager.create_conversation()
        self.chat_manager.add_message("user", "Hello!")

        conversation = self.chat_manager.active_conversation
        self.assertEqual(len(conversation.messages), 1)
        self.assertEqual(conversation.messages[0]["role"], "user")
        self.assertEqual(conversation.messages[0]["content"], "Hello!")

    def test_process_message(self):
        """Test processing a message and generating a response."""
        self.chat_manager.create_conversation()
        response = self.chat_manager.process_message("Hello!")

        conversation = self.chat_manager.active_conversation
        self.assertEqual(len(conversation.messages), 2)
        self.assertEqual(conversation.messages[0]["role"], "user")
        self.assertEqual(conversation.messages[0]["content"], "Hello!")
        self.assertEqual(conversation.messages[1]["role"], "assistant")
        self.assertEqual(conversation.messages[1]["content"], response)
        self.assertEqual(response, "Mock LLM received: Hello!")

    def test_save_and_load_conversation(self):
        """Test saving and loading a conversation."""
        # Create and save a conversation
        original = self.chat_manager.create_conversation(
            user_id="test-user",
            title="Test Conversation"
        )
        self.chat_manager.add_message("user", "Hello!")
        self.chat_manager.add_message("assistant", "Hi there!")

        file_path = self.chat_manager.save_conversation()

        # Load the conversation
        self.chat_manager.active_conversation = None
        loaded = self.chat_manager.load_conversation(original.conversation_id)

        self.assertIsNotNone(loaded)
        self.assertEqual(loaded.conversation_id, original.conversation_id)
        self.assertEqual(loaded.user_id, original.user_id)
        self.assertEqual(loaded.title, original.title)
        self.assertEqual(len(loaded.messages), 2)
        self.assertEqual(loaded.messages[0]["role"], "user")
        self.assertEqual(loaded.messages[0]["content"], "Hello!")
        self.assertEqual(loaded.messages[1]["role"], "assistant")
        self.assertEqual(loaded.messages[1]["content"], "Hi there!")

    def test_list_conversations(self):
        """Test listing conversations."""
        # Create a few conversations
        for i in range(3):
            self.chat_manager.create_conversation(title=f"Conversation {i}")
            self.chat_manager.add_message("user", f"Message {i}")
            self.chat_manager.save_conversation()

        conversations = self.chat_manager.list_conversations()
        self.assertEqual(len(conversations), 3)

    def test_set_active_conversation(self):
        """Test setting the active conversation."""
        # Create and save a conversation
        original = self.chat_manager.create_conversation(title="Test Conversation")
        self.chat_manager.add_message("user", "Hello!")
        self.chat_manager.save_conversation()

        # Clear the active conversation
        self.chat_manager.active_conversation = None

        # Set the active conversation
        conversation = self.chat_manager.set_active_conversation(original.conversation_id)

        self.assertIsNotNone(conversation)
        self.assertEqual(conversation.conversation_id, original.conversation_id)
        self.assertEqual(conversation.title, "Test Conversation")
        self.assertEqual(self.chat_manager.active_conversation, conversation)

    def test_custom_llm(self):
        """Test using a custom LLM."""
        # Define a custom response function
        def custom_response(prompt, _context, _kwargs):
            # Ignore _context and _kwargs for this test
            return f"Custom: {prompt}"

        # Create a custom MockLLM
        custom_llm = MockLLM(response_func=custom_response)

        # Create a ChatManager with the custom LLM
        chat_manager = ChatManager(storage_dir=self.temp_dir, llm=custom_llm)

        # Create a conversation and process a message
        chat_manager.create_conversation()
        response = chat_manager.process_message("Hello!")

        self.assertEqual(response, "Custom: Hello!")

    def test_error_no_active_conversation(self):
        """Test that errors are raised when no active conversation exists."""
        # No active conversation
        with self.assertRaises(ValueError):
            self.chat_manager.add_message("user", "Hello!")

        with self.assertRaises(ValueError):
            self.chat_manager.process_message("Hello!")

        with self.assertRaises(ValueError):
            self.chat_manager.save_conversation()

    def test_conversation_created_date(self):
        """Test the created_date property."""
        conversation = self.chat_manager.create_conversation()
        created_date = conversation.created_date

        self.assertIsInstance(created_date, datetime)
        self.assertEqual(created_date.isoformat(), conversation.created_at)

    def test_filter_conversations_by_user(self):
        """Test filtering conversations by user ID."""
        # Create conversations for different users
        self.chat_manager.create_conversation(user_id="user1", title="User 1 Conversation")
        self.chat_manager.save_conversation()

        self.chat_manager.create_conversation(user_id="user2", title="User 2 Conversation")
        self.chat_manager.save_conversation()

        self.chat_manager.create_conversation(user_id="user1", title="Another User 1 Conversation")
        self.chat_manager.save_conversation()

        # List conversations for user1
        user1_conversations = self.chat_manager.list_conversations(user_id="user1")
        self.assertEqual(len(user1_conversations), 2)
        for conv in user1_conversations:
            self.assertEqual(conv["user_id"], "user1")

        # List conversations for user2
        user2_conversations = self.chat_manager.list_conversations(user_id="user2")
        self.assertEqual(len(user2_conversations), 1)
        self.assertEqual(user2_conversations[0]["user_id"], "user2")

    def test_filter_conversations_by_date(self):
        """Test filtering conversations by date."""
        # This test is a bit tricky since we can't easily manipulate creation dates
        # We'll just verify that the filtering parameters are accepted

        # Create a few conversations
        for i in range(3):
            self.chat_manager.create_conversation(title=f"Conversation {i}")
            self.chat_manager.save_conversation()

        # Get the current year and month
        now = datetime.now()
        year = str(now.year)
        month = f"{now.month:02d}"

        # List conversations for the current year
        year_conversations = self.chat_manager.list_conversations(year=year)
        self.assertEqual(len(year_conversations), 3)

        # List conversations for the current year and month
        month_conversations = self.chat_manager.list_conversations(year=year, month=month)
        self.assertEqual(len(month_conversations), 3)


if __name__ == "__main__":
    unittest.main()
