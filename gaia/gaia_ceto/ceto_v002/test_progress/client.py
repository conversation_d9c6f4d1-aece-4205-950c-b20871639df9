from fastmcp import Client
import asyncio

async def interact_with_server():
    print("--- Creating Client ---")

    # Option 1: Connect to a server run via `python my_server.py` (uses stdio)
    # client = Client("my_server.py")

    # Option 2: Connect to a server run via `fastmcp run ... --transport sse --port 8080`
    #client = Client("http://localhost:9000") # Use the correct URL/port


    try:
        async with Client("http://127.0.0.1:9000") as client:
            print("--- Client Connected ---")
            # Call the 'greet' tool
            await client.call_tool("long_task")
    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        print("--- Client Interaction Finished ---")

if __name__ == "__main__":
    asyncio.run(interact_with_server())
