from fastmcp import FastMCP, Context
import asyncio

mcp = FastMCP("ProgressServer")

@mcp.tool()
async def long_task(ctx: Context):
    for i in range(5):
        await asyncio.sleep(1)
        await ctx.report_progress(i + 1, 5)
        await ctx.info(f"Completed step {i + 1}")
    return {"status": "done"}

if __name__ == "__main__":
    mcp.run(transport="streamable-http", host="127.0.0.1", port=9000)
