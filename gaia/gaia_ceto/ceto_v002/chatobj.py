"""Enhanced chat object with improved chronological storage for managing thousands of conversations."""

import json
import logging
import os
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional, Protocol, Callable

from gaia.gaia_ceto.ceto_v002.chrono_store import ChronoStore

logger = logging.getLogger(__name__)


class LLMInterface(Protocol):
    """Protocol defining the interface for LLM implementations."""

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a response to the given prompt with conversation context."""
        ...


class MockLLM:
    """A mock LLM implementation for testing purposes."""

    def __init__(self, response_func: Optional[Callable[[str, List[Dict[str, Any]], Dict[str, Any]], str]] = None):
        """Initialize the mock LLM.

        Args:
            response_func: Optional function to generate responses. If not provided,
                           a simple echo function is used.
        """
        self._response_func = response_func or self._default_response

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a mock response to the given prompt.

        Args:
            prompt: The user's input text.
            context: The conversation history.
            **kwargs: Additional parameters to pass to the response function.

        Returns:
            A string response.
        """
        return self._response_func(prompt, context, kwargs)

    @staticmethod
    def _default_response(prompt: str, _context: List[Dict[str, Any]], _kwargs: Dict[str, Any]) -> str:
        """Default response function that echoes the input with a prefix.

        Args:
            prompt: The user's input text.
            _context: The conversation history (unused in default implementation).
            _kwargs: Additional parameters (unused in default implementation).

        Returns:
            A simple response string.
        """
        return f"Mock LLM received: {prompt}"


class OpenAILLM:
    """OpenAI implementation of the LLM interface."""

    def __init__(self, model_name: str = "gpt-3.5-turbo", temperature: float = 0.7):
        """Initialize the OpenAI LLM.

        Args:
            model_name: The name of the OpenAI model to use.
            temperature: The temperature to use for generation.
        """
        self.model_name = model_name
        self.temperature = temperature

        # Import OpenAI here to avoid dependency issues if not using this implementation
        try:
            import openai
            # Get API key from environment variable
            api_key = os.environ.get("OPENAI_API_KEY")

            # Check if API key is available
            if not api_key:
                logger.error("OpenAI API key not found in environment variables. Please set OPENAI_API_KEY.")
                raise ValueError("OpenAI API key not found in environment variables. Please set OPENAI_API_KEY.")

            self.client = openai.OpenAI(api_key=api_key)
        except ImportError:
            logger.error("OpenAI package not installed. Please install it with 'pip install openai'.")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            raise

    @classmethod
    def get_available_models(cls):
        """Get available models from OpenAI API.

        Returns:
            A list of available model names.
        """
        try:
            import openai
            api_key = os.environ.get("OPENAI_API_KEY")
            if not api_key:
                logger.warning("OpenAI API key not found in environment variables.")
                return ["gpt-3.5-turbo", "gpt-4"]  # Default models as fallback

            client = openai.OpenAI(api_key=api_key)
            models = client.models.list()

            # Filter for chat models only
            chat_models = [
                model.id for model in models
                if model.id.startswith(("gpt-3.5", "gpt-4")) and "vision" not in model.id
            ]

            # Sort models by version (newest first)
            chat_models.sort(reverse=True)

            return chat_models if chat_models else ["gpt-3.5-turbo", "gpt-4"]
        except Exception as e:
            logger.error(f"Error fetching OpenAI models: {e}")
            return ["gpt-3.5-turbo", "gpt-4"]  # Default models as fallback

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a response using the OpenAI API.

        Args:
            prompt: The user's input text.
            context: The conversation history.
            **kwargs: Additional parameters to pass to the OpenAI API.

        Returns:
            The generated response.
        """
        try:
            # Format the messages for the OpenAI API
            messages = []

            # Add the conversation history
            for msg in context:
                if msg["role"] in ["user", "assistant", "system"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })

            # Add the current prompt
            messages.append({
                "role": "user",
                "content": prompt
            })

            # Call the OpenAI API
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", 1000)
            )

            # Extract and return the response text
            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Error generating response from OpenAI: {e}")
            return f"Error generating response: {str(e)}"


class AnthropicLLM:
    """Anthropic Claude implementation of the LLM interface."""

    def __init__(self, model_name: str = "claude-3-sonnet-20240229", temperature: float = 0.7):
        """Initialize the Anthropic LLM.

        Args:
            model_name: The name of the Anthropic model to use.
            temperature: The temperature to use for generation.
        """
        self.model_name = model_name
        self.temperature = temperature

        # Import Anthropic here to avoid dependency issues if not using this implementation
        try:
            import anthropic
            # Get API key from environment variable
            api_key = os.environ.get("ANTHROPIC_API_KEY")

            # Check if API key is available
            if not api_key:
                logger.error("Anthropic API key not found in environment variables. Please set ANTHROPIC_API_KEY.")
                raise ValueError("Anthropic API key not found in environment variables. Please set ANTHROPIC_API_KEY.")

            self.client = anthropic.Anthropic(api_key=api_key)
        except ImportError:
            logger.error("Anthropic package not installed. Please install it with 'pip install anthropic'.")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize Anthropic client: {e}")
            raise

    @classmethod
    def get_available_models(cls):
        """Get available models from Anthropic.

        Returns:
            A list of available model names.
        """
        try:
            import anthropic
            api_key = os.environ.get("ANTHROPIC_API_KEY")
            if not api_key:
                logger.warning("Anthropic API key not found in environment variables.")
                return ["claude-3-sonnet-20240229", "claude-3-opus-20240229"]  # Default models as fallback

            # Anthropic doesn't have a list models endpoint in their Python SDK
            # We'll use a predefined list of their latest models
            # This could be updated when they add this functionality
            models = [
                "claude-3-opus-20240229",
                "claude-3-sonnet-20240229",
                "claude-3-haiku-20240307",
                "claude-2.1",
                "claude-2.0",
                "claude-instant-1.2"
            ]

            return models
        except Exception as e:
            logger.error(f"Error setting up Anthropic client: {e}")
            return ["claude-3-sonnet-20240229", "claude-3-opus-20240229"]  # Default models as fallback

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a response using the Anthropic API.

        Args:
            prompt: The user's input text.
            context: The conversation history.
            **kwargs: Additional parameters to pass to the Anthropic API.

        Returns:
            The generated response.
        """
        try:
            # Format the messages for the Anthropic API
            messages = []

            # Add the conversation history
            for msg in context:
                if msg["role"] in ["user", "assistant"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                elif msg["role"] == "system":
                    # System messages are handled differently in Anthropic
                    # We'll add them as a system parameter later
                    pass

            # Add the current prompt
            messages.append({
                "role": "user",
                "content": prompt
            })

            # Extract system messages
            system_messages = [msg["content"] for msg in context if msg["role"] == "system"]
            system_prompt = "\n".join(system_messages) if system_messages else None

            # Call the Anthropic API
            response = self.client.messages.create(
                model=self.model_name,
                messages=messages,
                system=system_prompt,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", 1000)
            )

            # Extract and return the response text
            return response.content[0].text

        except Exception as e:
            logger.error(f"Error generating response from Anthropic: {e}")
            return f"Error generating response: {str(e)}"


class Conversation:
    """A class representing a single conversation with metadata and messages."""

    def __init__(self,
                 conversation_id: Optional[str] = None,
                 user_id: Optional[str] = None,
                 title: Optional[str] = None,
                 metadata: Optional[Dict[str, Any]] = None):
        """Initialize a new Conversation.

        Args:
            conversation_id: Unique identifier for the conversation. If not provided,
                            a UUID will be generated.
            user_id: Optional identifier for the user who owns this conversation.
            title: Optional title for the conversation.
            metadata: Optional dictionary of additional metadata.
        """
        self.conversation_id = conversation_id or str(uuid.uuid4())
        self.user_id = user_id
        self.title = title or f"Conversation {self.conversation_id[:8]}"
        self.metadata = metadata or {}
        self.messages: List[Dict[str, Any]] = []
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at

    def add_message(self, role: str, content: str) -> None:
        """Add a message to the conversation.

        Args:
            role: The role of the message sender (e.g., "user", "assistant", "system").
            content: The content of the message.
        """
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        self.messages.append(message)
        self.updated_at = message["timestamp"]

    def to_dict(self) -> Dict[str, Any]:
        """Convert the conversation to a dictionary for serialization.

        Returns:
            A dictionary representation of the conversation.
        """
        return {
            "conversation_id": self.conversation_id,
            "user_id": self.user_id,
            "title": self.title,
            "metadata": self.metadata,
            "messages": self.messages,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Conversation':
        """Create a Conversation instance from a dictionary.

        Args:
            data: Dictionary containing conversation data.

        Returns:
            A new Conversation instance.
        """
        conversation = cls(
            conversation_id=data.get("conversation_id"),
            user_id=data.get("user_id"),
            title=data.get("title"),
            metadata=data.get("metadata", {})
        )
        conversation.messages = data.get("messages", [])
        conversation.created_at = data.get("created_at", conversation.created_at)
        conversation.updated_at = data.get("updated_at", conversation.updated_at)
        return conversation

    @property
    def created_date(self) -> datetime:
        """Get the creation date as a datetime object.

        Returns:
            A datetime object representing the creation date.
        """
        return datetime.fromisoformat(self.created_at)


class ChatManager:
    """A manager for multiple conversations with chronological storage capabilities."""

    def __init__(self,
                 storage_dir: str = "./conversations",
                 llm: Optional[LLMInterface] = None):
        """Initialize a new ChatManager.

        Args:
            storage_dir: Base directory to store conversation files.
            llm: An LLM implementation to use for generating responses.
                 If not provided, a default MockLLM is used.
        """
        self.store = ChronoStore(storage_dir)
        self.llm = llm or MockLLM()
        self.active_conversation: Optional[Conversation] = None

    def create_conversation(self,
                           user_id: Optional[str] = None,
                           title: Optional[str] = None,
                           metadata: Optional[Dict[str, Any]] = None) -> Conversation:
        """Create a new conversation and set it as active.

        Args:
            user_id: Optional identifier for the user who owns this conversation.
            title: Optional title for the conversation.
            metadata: Optional dictionary of additional metadata.

        Returns:
            The newly created Conversation.
        """
        conversation = Conversation(user_id=user_id, title=title, metadata=metadata)
        self.active_conversation = conversation
        return conversation

    def set_active_conversation(self, conversation_id_or_path: str) -> Optional[Conversation]:
        """Set the active conversation by ID or path, loading it if necessary.

        Args:
            conversation_id_or_path: Either a conversation ID or a full path (YYYY/MM/conversation_id.json).

        Returns:
            The active Conversation if found, None otherwise.
        """
        conversation = self.load_conversation(conversation_id_or_path)
        if conversation:
            self.active_conversation = conversation
        return self.active_conversation

    def add_message(self, role: str, content: str) -> None:
        """Add a message to the active conversation.

        Args:
            role: The role of the message sender.
            content: The content of the message.

        Raises:
            ValueError: If no active conversation exists.
        """
        if not self.active_conversation:
            raise ValueError("No active conversation. Create or load a conversation first.")

        self.active_conversation.add_message(role, content)

    def process_message(self, content: str, **kwargs) -> str:
        """Process a user message and generate a response using the LLM.

        Args:
            content: The user's message content.
            **kwargs: Additional parameters to pass to the LLM.

        Returns:
            The assistant's response.

        Raises:
            ValueError: If no active conversation exists.
        """
        if not self.active_conversation:
            raise ValueError("No active conversation. Create or load a conversation first.")

        # Add the user message
        self.add_message("user", content)

        # Generate a response using the LLM
        response = self.llm.generate_response(
            prompt=content,
            context=self.active_conversation.messages,
            **kwargs
        )

        # Add the assistant's response to the conversation
        self.add_message("assistant", response)

        return response

    def save_conversation(self, conversation: Optional[Conversation] = None) -> str:
        """Save a conversation to disk using chronological storage.

        Args:
            conversation: The conversation to save. If not provided, saves the active conversation.

        Returns:
            The path to the saved file.

        Raises:
            ValueError: If no conversation is provided and no active conversation exists.
        """
        conversation = conversation or self.active_conversation
        if not conversation:
            raise ValueError("No conversation to save.")

        # Use the conversation's creation date for storage
        created_date = conversation.created_date

        # Save the conversation using ChronoStore
        filename = f"{conversation.conversation_id}.json"
        file_path = self.store.save_json(
            data=conversation.to_dict(),
            filename=filename,
            date=created_date
        )

        logger.info(f"Conversation saved to {file_path}")
        return file_path

    def load_conversation(self, conversation_id_or_path: str) -> Optional[Conversation]:
        """Load a conversation from disk.

        Args:
            conversation_id_or_path: Either a conversation ID or a full path (YYYY/MM/conversation_id.json).

        Returns:
            The loaded Conversation if found, None otherwise.
        """
        # Check if it's a full path or just an ID
        if os.path.sep in conversation_id_or_path:
            # It's a path
            if not conversation_id_or_path.endswith('.json'):
                conversation_id_or_path = f"{conversation_id_or_path}.json"

            # Load using the path
            data = self.store.load_json(conversation_id_or_path)
        else:
            # It's just an ID, find the file
            filename = f"{conversation_id_or_path}.json"
            data = self.store.load_json(filename)

        if not data:
            return None

        try:
            conversation = Conversation.from_dict(data)
            logger.info(f"Conversation loaded: {conversation.conversation_id}")
            return conversation
        except Exception as e:
            logger.error(f"Failed to load conversation: {e}")
            return None

    def list_conversations(self,
                          user_id: Optional[str] = None,
                          year: Optional[str] = None,
                          month: Optional[str] = None,
                          limit: int = 100,
                          reverse: bool = True) -> List[Dict[str, Any]]:
        """List available conversations, optionally filtered by date and user ID.

        Args:
            user_id: Optional user ID to filter conversations.
            year: Optional year to filter by (e.g., "2023").
            month: Optional month to filter by (e.g., "01").
            limit: Maximum number of conversations to return.
            reverse: Whether to return conversations in reverse chronological order (newest first).

        Returns:
            A list of conversation metadata dictionaries.
        """
        # Define a processor function to extract conversation metadata
        def extract_metadata(file_path: str) -> Optional[Dict[str, Any]]:
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)

                # Filter by user_id if provided
                if user_id and data.get("user_id") != user_id:
                    return None

                # Get the relative path for easier reference
                rel_path = self.store.get_relative_path(file_path)

                # Get the title or generate one from the first user message
                title = data.get("title")

                # If no explicit title, use the first 4 words of the first user message
                if not title or title.startswith("Conversation "):
                    messages = data.get("messages", [])
                    user_messages = [msg for msg in messages if msg.get("role") == "user"]

                    if user_messages:
                        # Get the first user message
                        first_user_msg = user_messages[0].get("content", "")
                        # Extract the first 4 words (or fewer if the message is shorter)
                        words = first_user_msg.split()[:4]
                        if words:
                            generated_title = " ".join(words)
                            # Add ellipsis if the message is longer than 4 words
                            if len(first_user_msg.split()) > 4:
                                generated_title += "..."
                            title = generated_title

                # Extract metadata for listing
                return {
                    "conversation_id": data.get("conversation_id"),
                    "title": title,
                    "user_id": data.get("user_id"),
                    "created_at": data.get("created_at"),
                    "updated_at": data.get("updated_at"),
                    "message_count": len(data.get("messages", [])),
                    "file_path": file_path,
                    "relative_path": rel_path
                }
            except Exception as e:
                logger.error(f"Error reading conversation file {file_path}: {e}")
                return None

        # If no specific date filters are provided and we want the most recent conversations,
        # we can use get_latest_files for better performance
        if not any([year, month]) and reverse and limit <= 100:
            latest_files = self.store.get_latest_files(count=limit * 2, extension=".json")
            results = []
            for file_path in latest_files:
                metadata = extract_metadata(file_path)
                if metadata:
                    results.append(metadata)
                if len(results) >= limit:
                    break
            return results

        # Otherwise, list files with the given filters
        files = self.store.list_files(year=year, month=month, extension=".json", reverse=reverse)

        results = []
        for file_path in files:
            metadata = extract_metadata(file_path)
            if metadata:
                results.append(metadata)
            if len(results) >= limit:
                break

        return results

    def delete_conversation(self, conversation_id_or_path: str) -> bool:
        """Delete a conversation from disk.

        Args:
            conversation_id_or_path: Either a conversation ID or a full path (YYYY/MM/conversation_id.json).

        Returns:
            True if the conversation was deleted, False otherwise.
        """
        # Extract the conversation ID from the path or use the ID directly
        if os.path.sep in conversation_id_or_path:
            # It's a path
            if conversation_id_or_path.endswith('.json'):
                file_path = os.path.join(self.store.base_dir, conversation_id_or_path)
                conversation_id = os.path.basename(file_path).replace('.json', '')
            else:
                # Path doesn't include .json extension
                file_path = os.path.join(self.store.base_dir, f"{conversation_id_or_path}.json")
                conversation_id = os.path.basename(conversation_id_or_path)
        else:
            # It's just an ID
            conversation_id = conversation_id_or_path
            filename = f"{conversation_id}.json"
            file_path = self.store.find_file(filename)

            if not file_path:
                logger.warning(f"Conversation file not found: {filename}")
                return False

        try:
            # Delete the file
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Conversation deleted: {file_path}")

                # Reset active conversation if it was the deleted one
                if (self.active_conversation and
                    self.active_conversation.conversation_id == conversation_id):
                    self.active_conversation = None

                # Clean up empty directories
                self.store.cleanup_empty_dirs()

                return True
            else:
                logger.warning(f"Conversation file not found: {file_path}")
                return False
        except Exception as e:
            logger.error(f"Failed to delete conversation: {e}")
            return False

    def get_conversation_stats(self) -> Dict[str, Any]:
        """Get statistics about stored conversations.

        Returns:
            A dictionary with statistics.
        """
        years = self.store.get_years()
        stats = {
            "total_conversations": 0,
            "years": {},
            "users": {}
        }

        user_counts = {}

        # Process each year
        for year in years:
            stats["years"][year] = {"total": 0, "months": {}}
            months = self.store.get_months(year)

            # Process each month
            for month in months:
                month_files = self.store.list_files(year=year, month=month, extension=".json")
                stats["years"][year]["months"][month] = len(month_files)
                stats["years"][year]["total"] += len(month_files)
                stats["total_conversations"] += len(month_files)

                # Count conversations by user
                for file_path in month_files:
                    try:
                        with open(file_path, 'r') as f:
                            data = json.load(f)
                        user_id = data.get("user_id", "anonymous")
                        user_counts[user_id] = user_counts.get(user_id, 0) + 1
                    except Exception:
                        pass

        # Add user statistics
        stats["users"] = user_counts

        return stats


# Example usage
if __name__ == "__main__":
    import shutil

    # Set up logging
    logging.basicConfig(level=logging.INFO,
                       format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Test directory
    test_dir = "./test_conversations"

    # Clean up any previous test data
    if os.path.exists(test_dir):
        shutil.rmtree(test_dir)
        print(f"Cleaned up previous test directory: {test_dir}")

    # Create a custom response function for testing
    def custom_response(prompt: str, context: List[Dict[str, Any]], _kwargs: Dict[str, Any]) -> str:
        if "hello" in prompt.lower():
            return "Hello! How can I help you today?"
        elif "help" in prompt.lower():
            return "I'm here to assist you. What do you need help with?"
        elif "history" in prompt.lower():
            # Demonstrate using context
            user_msgs = [msg["content"] for msg in context if msg["role"] == "user"]
            return f"You've sent {len(user_msgs)} messages in this conversation."
        else:
            return f"I received your message: '{prompt}'. How can I assist you further?"

    # Create a chat manager
    manager = ChatManager(storage_dir=test_dir, llm=MockLLM(custom_response))

    # Create and use multiple conversations
    print("\n=== Creating and Using Multiple Conversations ===")

    # Create first conversation
    conv1 = manager.create_conversation(title="Test Conversation 1", user_id="user1")
    print(f"Created conversation: {conv1.title} (ID: {conv1.conversation_id})")

    # Add some messages
    response1 = manager.process_message("Hello there!")
    print(f"User: Hello there!\nAssistant: {response1}")

    response2 = manager.process_message("Tell me about my history")
    print(f"User: Tell me about my history\nAssistant: {response2}")

    # Save the conversation
    path1 = manager.save_conversation()
    print(f"Saved conversation to: {path1}")

    # Create second conversation
    conv2 = manager.create_conversation(title="Test Conversation 2", user_id="user2")
    print(f"\nCreated conversation: {conv2.title} (ID: {conv2.conversation_id})")

    # Add some messages
    response3 = manager.process_message("I need help with something")
    print(f"User: I need help with something\nAssistant: {response3}")

    # Save the conversation
    path2 = manager.save_conversation()
    print(f"Saved conversation to: {path2}")

    # List all conversations
    print("\n=== Listing Conversations ===")
    conversations = manager.list_conversations()
    for i, conv in enumerate(conversations, 1):
        print(f"{i}. {conv['title']} (ID: {conv['conversation_id']})")
        print(f"   Created: {conv['created_at']}")
        print(f"   Messages: {conv['message_count']}")
        print(f"   Path: {conv['relative_path']}")

    # Load a conversation by ID
    print("\n=== Loading Conversation by ID ===")
    loaded_conv = manager.load_conversation(conv1.conversation_id)
    if loaded_conv:
        print(f"Loaded conversation: {loaded_conv.title}")
        print(f"Messages: {len(loaded_conv.messages)}")
        for msg in loaded_conv.messages:
            print(f"{msg['role'].capitalize()}: {msg['content']}")

    # Load a conversation by path
    print("\n=== Loading Conversation by Path ===")
    rel_path = os.path.relpath(path2, manager.store.base_dir)
    loaded_conv2 = manager.load_conversation(rel_path)
    if loaded_conv2:
        print(f"Loaded conversation by path: {loaded_conv2.title}")
        print(f"Messages: {len(loaded_conv2.messages)}")

    # Get statistics
    print("\n=== Conversation Statistics ===")
    stats = manager.get_conversation_stats()
    print(f"Total conversations: {stats['total_conversations']}")

    print("\nBy Year/Month:")
    for year, year_data in stats["years"].items():
        print(f"  {year}: {year_data['total']} conversations")
        for month, count in year_data["months"].items():
            print(f"    {month}: {count} conversations")

    print("\nBy User:")
    for user_id, count in stats["users"].items():
        print(f"  {user_id}: {count} conversations")

    print("\n=== Test Complete ===")
    print("To try the interactive interface, run:")
    print("python chat_term.py --storage-dir ./conversations")
