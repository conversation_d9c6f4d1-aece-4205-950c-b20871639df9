#!/usr/bin/env python3
"""
Test script to verify FastMCP Context functionality
"""

import asyncio
import time
from fastmcp import Context

async def test_process_items_with_context():
    """Test the process_items pattern with FastMCP Context"""
    
    # Mock Context for testing
    class MockContext:
        def __init__(self):
            self.progress_reports = []
            self.info_messages = []
        
        async def report_progress(self, progress: int, total: int):
            """Mock progress reporting"""
            timestamp = time.strftime('%H:%M:%S')
            message = f"[{timestamp}] Progress: {progress}/{total} ({(progress/total)*100:.1f}%)"
            self.progress_reports.append({"progress": progress, "total": total, "timestamp": timestamp})
            print(f"📊 PROGRESS: {message}")
        
        async def info(self, message: str):
            """Mock info logging"""
            timestamp = time.strftime('%H:%M:%S')
            formatted_message = f"[{timestamp}] INFO: {message}"
            self.info_messages.append({"message": message, "timestamp": timestamp})
            print(f"ℹ️  INFO: {formatted_message}")
    
    # Test the pattern from your template
    async def process_items(items: list[str], ctx: Context) -> dict:
        """Process items with progress reporting - your template pattern"""
        total = len(items)
        results = []

        for i, item in enumerate(items):
            # Report progress
            await ctx.report_progress(progress=i, total=total)
            await ctx.info(f"Processing item {i+1}/{total}: {item}")

            # Simulate processing
            await asyncio.sleep(0.1)
            results.append(item.upper())

        # Report completion
        await ctx.report_progress(progress=total, total=total)
        await ctx.info(f"🎉 All {total} items processed successfully")

        return {"processed": len(results), "results": results}
    
    # Test with mock context
    print("🧪 Testing FastMCP Context pattern...")
    
    mock_ctx = MockContext()
    test_items = ["fastmcp", "test", "progress", "demo", "streaming"]
    
    start_time = time.time()
    result = await process_items(test_items, mock_ctx)
    end_time = time.time()
    
    print(f"\n✅ Test completed in {end_time - start_time:.2f} seconds")
    print(f"📊 Progress reports: {len(mock_ctx.progress_reports)}")
    print(f"ℹ️  Info messages: {len(mock_ctx.info_messages)}")
    print(f"🎯 Final result: {result}")
    
    # Verify progress reporting
    assert len(mock_ctx.progress_reports) == len(test_items) + 1  # One for each item + final
    assert mock_ctx.progress_reports[-1]["progress"] == len(test_items)  # Final progress should be total
    assert len(result["results"]) == len(test_items)
    
    print("\n🎉 All tests passed! FastMCP Context pattern is working correctly.")
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(test_process_items_with_context())
        print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}: FastMCP Context test completed")
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
