import streamlit as st
import asyncio
import sys
import json
import time
from typing import Optional, List, Dict, Any
from contextlib import AsyncExitStack
import pandas as pd
from datetime import datetime

# Import ClientSession and types from mcp core
from mcp import ClientSession, types
# Import sse_client specifically for SSE transport
from mcp.client.sse import sse_client

from anthropic import Anthropic
from dotenv import load_dotenv
import os

# Load environment variables from .env file
load_dotenv()

# --- Configuration ---
DEFAULT_SERVER_URL = "http://0.0.0.0:8000/sse"  # Default URL for SSE

# Helper functions
def format_json(obj):
    """Format JSON for display in Streamlit."""
    if obj is None:
        return "None"
    try:
        if isinstance(obj, str):
            # Try to parse as JSON if it's a string
            try:
                parsed = json.loads(obj)
                return json.dumps(parsed, indent=2)
            except:
                return obj
        else:
            # Convert to JSON if it's an object
            return json.dumps(obj, indent=2, default=str)
    except:
        return str(obj)

def debug_messages(label: str, messages: List[Dict[str, Any]], container=None):
    """Debug helper to print message content and store in session state."""
    # Store in session state for persistent display
    if 'debug_logs' not in st.session_state:
        st.session_state.debug_logs = []

    # Create a summary of the messages
    summary = f"**{label}** - {len(messages)} messages\n\n"

    for i, msg in enumerate(messages):
        role = msg.get('role', 'unknown')
        content = msg.get('content', '')

        summary += f"**Message {i}** ({role}):\n"

        # Summarize content based on type
        if isinstance(content, list):
            summary += f"*Complex content with {len(content)} blocks*\n"
            for j, block in enumerate(content):
                if isinstance(block, dict) and 'type' in block:
                    summary += f"- Block {j}: {block.get('type', 'unknown')}\n"
                else:
                    summary += f"- Block {j}\n"
        elif isinstance(content, str):
            if len(content) > 100:
                summary += f"*Text content ({len(content)} chars)*: {content[:100]}...\n"
            else:
                summary += f"*Text content*: {content}\n"
        else:
            summary += "*Complex content*\n"

    # Add to session state
    debug_entry = {
        "title": f"🔍 {label} ({len(messages)} messages)",
        "content": summary,
        "timestamp": datetime.now().isoformat(),
        "messages": messages  # Store the full messages for reference
    }

    # Add to the beginning of the list (most recent first)
    st.session_state.debug_logs.insert(0, debug_entry)

    # Limit the number of stored debug entries
    if len(st.session_state.debug_logs) > 20:
        st.session_state.debug_logs = st.session_state.debug_logs[:20]

    # Also display in the container if provided
    if container is not None:
        container.markdown(f"### 🔍 {label}")

        # Create a selectbox to choose which message to view
        if messages:
            message_options = [f"Message {i} ({msg.get('role', 'unknown')})" for i, msg in enumerate(messages)]
            selected_message = container.selectbox(f"Select message from {label}:", message_options, key=f"select_{label}")

            # Get the index of the selected message
            if selected_message:
                selected_index = message_options.index(selected_message)
                msg = messages[selected_index]
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')

                container.markdown(f"**Selected: Message {selected_index}** ({role})")

                # Handle different content types
                if isinstance(content, list):
                    container.markdown(f"*Complex content with {len(content)} blocks*")

                    # Create a selectbox for blocks
                    block_options = [f"Block {j}" for j in range(len(content))]
                    selected_block = container.selectbox("Select content block:", block_options, key=f"block_{label}")

                    if selected_block:
                        block_index = block_options.index(selected_block)
                        block = content[block_index]
                        container.code(format_json(block), language="json")

                elif isinstance(content, str):
                    if len(content) > 500:
                        container.markdown(f"*Long text content ({len(content)} chars)*")
                        container.code(content[:500] + "...", language="text")
                        if container.button("Show full content", key=f"full_{label}"):
                            container.code(content, language="text")
                    else:
                        container.code(content, language="text")
                else:
                    container.code(format_json(content), language="json")

def debug_response(label: str, response, container=None):
    """Debug helper to print Anthropic response details and store in session state."""
    # Store in session state for persistent display
    if 'debug_logs' not in st.session_state:
        st.session_state.debug_logs = []

    # Create a summary of the response
    summary = f"**{label}**\n\n"

    # Check if response is likely an Anthropic Messages API response object
    if hasattr(response, 'content') and hasattr(response, 'stop_reason'):
        summary += f"**Stop Reason:** {response.stop_reason}\n\n"
        summary += "**Content Blocks:**\n"

        for i, block in enumerate(response.content):
            block_type = block.type
            if block_type == 'text':
                text = block.text
                if len(text) > 100:
                    summary += f"- Block {i}: Text ({len(text)} chars) - {text[:100]}...\n"
                else:
                    summary += f"- Block {i}: Text - {text}\n"
            elif block_type == 'tool_use':
                summary += f"- Block {i}: Tool Use - {block.name} (ID: {block.id})\n"
            else:
                summary += f"- Block {i}: {block_type}\n"
    else:
        # Fallback for other response types
        summary += "Non-standard response format\n"

    # Add to session state
    debug_entry = {
        "title": f"🔍 {label}",
        "content": summary,
        "timestamp": datetime.now().isoformat(),
        "response": str(response)  # Store string representation for reference
    }

    # Add to the beginning of the list (most recent first)
    st.session_state.debug_logs.insert(0, debug_entry)

    # Limit the number of stored debug entries
    if len(st.session_state.debug_logs) > 20:
        st.session_state.debug_logs = st.session_state.debug_logs[:20]

    # Also display in the container if provided
    if container is not None:
        container.markdown(f"### 🔍 {label}")

        # Check if response is likely an Anthropic Messages API response object
        if hasattr(response, 'content') and hasattr(response, 'stop_reason'):
            container.markdown(f"**Stop Reason:** {response.stop_reason}")
            container.markdown("**Content Blocks:**")

            # Create a selectbox for content blocks
            if response.content:
                block_options = []
                for i, block in enumerate(response.content):
                    block_type = block.type
                    if block_type == 'text':
                        block_options.append(f"Block {i}: Text")
                    elif block_type == 'tool_use':
                        block_options.append(f"Block {i}: Tool Use - {block.name}")
                    else:
                        block_options.append(f"Block {i}: {block_type}")

                selected_block = container.selectbox(
                    "Select content block:",
                    block_options,
                    key=f"response_block_{label}"
                )

                if selected_block:
                    block_index = int(selected_block.split(':')[0].replace('Block ', ''))
                    content_block = response.content[block_index]
                    block_type = content_block.type

                    if block_type == 'text':
                        text = content_block.text
                        if len(text) > 500:
                            container.markdown(f"*Long text ({len(text)} chars)*")
                            container.code(text[:500] + "...", language="text")
                            if container.button("Show full text", key=f"full_text_{label}_{block_index}"):
                                container.code(text, language="text")
                        else:
                            container.code(text, language="text")

                    elif block_type == 'tool_use':
                        container.markdown(f"**Tool Name:** {content_block.name}")
                        container.markdown(f"**Tool Use ID:** {content_block.id}")
                        container.markdown("**Tool Input:**")
                        container.code(format_json(content_block.input), language="json")
        else:
            # Fallback for other response types
            container.code(str(response), language="text")

def debug_tool_call(tool_name, tool_input, tool_result, timing=None, container=None):
    """Debug helper to display detailed tool call information and store in session state."""
    # Tool metadata
    metadata = f"**Tool:** {tool_name}\n"
    if timing:
        metadata += f"**Execution Time:** {timing:.2f} seconds\n"

    # Input section
    metadata += f"\n**Input:**\n```json\n{format_json(tool_input)}\n```"

    # Result section
    metadata += "\n\n**Result:**"

    # Format the result content
    if hasattr(tool_result, 'isError') and tool_result.isError:
        result_status = "❌ Tool execution failed"
        result_content = tool_result.content if hasattr(tool_result, 'content') else "No error details"
    else:
        result_status = "✅ Tool executed successfully"
        if hasattr(tool_result, 'content'):
            content = tool_result.content
            # Check if it's tabular data
            if isinstance(content, str) and '\n' in content and '  ' in content:
                result_content = content
                is_tabular = True
            else:
                result_content = str(content)
                is_tabular = False
        else:
            result_content = format_json(tool_result)
            is_tabular = False

    # Store in session state for persistent display
    debug_entry = {
        "title": f"🛠️ Tool Call: {tool_name} ({result_status})",
        "content": metadata,
        "code": result_content,
        "language": "text",
        "timestamp": datetime.now().isoformat(),
        "is_tabular": is_tabular
    }

    if 'tool_debug_info' not in st.session_state:
        st.session_state.tool_debug_info = []

    # Add to the beginning of the list (most recent first)
    st.session_state.tool_debug_info.insert(0, debug_entry)

    # Limit the number of stored debug entries to prevent memory issues
    if len(st.session_state.tool_debug_info) > 20:
        st.session_state.tool_debug_info = st.session_state.tool_debug_info[:20]

    # Also display in the container if provided
    if container is not None:
        container.markdown(f"### 🛠️ Tool Call: {tool_name}")
        container.markdown(metadata)

        if hasattr(tool_result, 'isError') and tool_result.isError:
            container.error(result_status)
            container.code(result_content, language="text")
        else:
            container.success(result_status)
            if is_tabular:
                container.markdown("*Detected possible tabular data:*")
                container.text(result_content)
            elif len(result_content) > 500:
                container.markdown(f"*Long content ({len(result_content)} chars)*")
                container.code(result_content[:500] + "...", language="text")
            else:
                container.code(result_content, language="text")

class MCPClient:
    def __init__(self):
        # Initialize session and client objects
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.anthropic = Anthropic()  # Assumes ANTHROPIC_API_KEY is in env
        self.available_tools = []
        self.connected = False
        self.tool_calls_history = []  # Store history of tool calls for debugging

    async def connect_to_server(self, server_url: str):
        """Connect to an MCP server via SSE transport.

        Args:
            server_url: The HTTP URL of the server's SSE endpoint.
        """
        st.info(f"Connecting to MCP server via SSE at: {server_url}")
        try:
            # Establish SSE transport connection using the provided URL
            sse_transport = await self.exit_stack.enter_async_context(sse_client(server_url))
            read_stream, write_stream = sse_transport

            # Create and initialize the MCP ClientSession using the SSE streams
            self.session = await self.exit_stack.enter_async_context(ClientSession(read_stream, write_stream))
            st.info("Initializing MCP session (performing handshake)...")
            await self.session.initialize()

            # List available tools from the connected server
            response = await self.session.list_tools()
            tools = response.tools
            tool_names = [tool.name for tool in tools]
            st.success(f"Connected to server with tools: {', '.join(tool_names)}")

            # Prepare tools in the format expected by Anthropic API
            self.available_tools = []
            for tool in tools:
                self.available_tools.append({
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema
                })

            # Add a direct test of the echostring tool to verify it works at connection time
            if "echostring" in tool_names:
                st.info("Testing echostring tool directly after connection...")
                try:
                    # Test with dictionary format
                    test_result_dict = await self.session.call_tool("echostring", {"phrase": "connection test"})
                    st.success(f"✅ Echostring test with dict format succeeded: {test_result_dict.content}")
                except Exception as e_dict:
                    st.error(f"❌ Echostring test with dict format failed: {str(e_dict)}")

                    # Try with string format as fallback
                    try:
                        test_result_str = await self.session.call_tool("echostring", "connection test")
                        st.success(f"✅ Echostring test with string format succeeded: {test_result_str.content}")

                        # If string format works, patch the call_tool method to handle string inputs
                        original_call_tool = self.session.call_tool

                        async def patched_call_tool(tool_name, tool_input):
                            if tool_name == "echostring" and isinstance(tool_input, str):
                                st.sidebar.info("Using patched call_tool method for string input")
                                return await original_call_tool(tool_name, tool_input)
                            return await original_call_tool(tool_name, tool_input)

                        self.session.call_tool = patched_call_tool
                        st.info("📝 Patched call_tool method to handle string inputs for echostring")
                    except Exception as e_str:
                        st.error(f"❌ Both test formats failed. Dict error: {str(e_dict)}, String error: {str(e_str)}")

            self.connected = True
            return True

        except ConnectionRefusedError:
            st.error(f"Connection Error: Could not connect to the server at {server_url}.")
            st.error("Please ensure the MCP server process is running, accessible, and listening on the correct address and port with SSE enabled.")
            self.connected = False
            return False
        except Exception as e:
            st.error(f"An unexpected error occurred during connection or initialization: {e}")
            self.connected = False
            return False

    async def process_query(self, query: str) -> str:
        """Process a query using Claude and available tools via the MCP session."""
        if not self.session:
            return "Error: Not connected to MCP server."

        messages = [{"role": "user", "content": query}]
        final_response_parts = []  # Store parts of the final response text

        st.info("Processing query...")
        debug_messages("Initial User Query", messages)

        try:
            # Initial call to Claude
            with st.spinner("Thinking..."):
                response = self.anthropic.messages.create(
                    model="claude-3-5-sonnet-20240620",  # Use a known compatible model
                    max_tokens=1024,  # Adjusted token limit
                    messages=messages,
                    tools=self.available_tools
                )

            debug_response("Initial Claude Response", response)

            # Store the original response for later use
            original_response = response

            # Extract initial text response
            for content_block in response.content:
                if content_block.type == 'text':
                    final_response_parts.append(content_block.text)

            # Process tool calls if any were requested
            tool_use_blocks = [block for block in response.content if block.type == 'tool_use']

            if tool_use_blocks:
                st.info(f"Claude requested {len(tool_use_blocks)} tool call(s).")
                tool_results_content = []  # Content for the next user message to Claude

                # Add the assistant's message with tool_use blocks to the conversation
                # Manually construct the content blocks to ensure proper formatting
                assistant_content_blocks = []
                for content_block in original_response.content:
                    if content_block.type == 'text':
                        assistant_content_blocks.append({"type": "text", "text": content_block.text})
                    elif content_block.type == 'tool_use':
                        assistant_content_blocks.append({
                            "type": "tool_use",
                            "id": content_block.id,
                            "name": content_block.name,
                            "input": content_block.input,
                        })

                if assistant_content_blocks:
                    messages.append({"role": "assistant", "content": assistant_content_blocks})

                for tool_use in tool_use_blocks:
                    tool_name = tool_use.name
                    tool_input = tool_use.input
                    tool_call_id = tool_use.id  # Get the ID for the result

                    st.info(f"Calling tool: '{tool_name}' with input: {tool_input}")
                    # Add placeholder text to final response
                    final_response_parts.append(f"\n[Calling tool '{tool_name}'...]")

                    # Execute the tool call via MCP session
                    try:
                        # Create a tool call record for debugging
                        tool_call_record = {
                            "timestamp": datetime.now().isoformat(),
                            "tool_name": tool_name,
                            "tool_input": tool_input,
                            "tool_call_id": tool_call_id,
                            "status": "pending"
                        }

                        # Measure execution time
                        start_time = time.time()

                        # Ensure tool_input is properly formatted as a dictionary
                        formatted_input = tool_input

                        # Special handling for echostring tool
                        if tool_name == "echostring":
                            # Extract the actual phrase to echo
                            phrase_to_echo = ""

                            # Case 1: If input is a string, use it directly
                            if isinstance(tool_input, str):
                                phrase_to_echo = tool_input
                            # Case 2: If input is a dict with a "phrase" key, use that
                            elif isinstance(tool_input, dict) and "phrase" in tool_input:
                                phrase_to_echo = tool_input["phrase"]
                            # Case 3: If input is a dict without "phrase" key but with other keys
                            elif isinstance(tool_input, dict) and len(tool_input) > 0:
                                # Use the first value as the phrase
                                first_key = next(iter(tool_input))
                                phrase_to_echo = tool_input[first_key]
                            # Case 4: Default case
                            else:
                                phrase_to_echo = "empty input"

                            # If the phrase is a string that looks like a quoted string (e.g., '"hello"')
                            # remove the extra quotes
                            if isinstance(phrase_to_echo, str):
                                phrase_to_echo = phrase_to_echo.strip()
                                if (phrase_to_echo.startswith('"') and phrase_to_echo.endswith('"')) or \
                                   (phrase_to_echo.startswith("'") and phrase_to_echo.endswith("'")):
                                    phrase_to_echo = phrase_to_echo[1:-1]

                            # Create a properly formatted input dictionary
                            formatted_input = {"phrase": phrase_to_echo}

                            # Add extra debug information
                            st.sidebar.markdown("### 🔍 Echostring Tool Debug")
                            st.sidebar.markdown(f"**Original Input:** `{tool_input}`")
                            st.sidebar.markdown(f"**Extracted Phrase:** `{phrase_to_echo}`")
                            st.sidebar.markdown(f"**Formatted Input:** `{formatted_input}`")

                        # CRITICAL DEBUG: Store debug info in session state
                        if 'debug_entries' not in st.session_state:
                            st.session_state.debug_entries = []

                        # Create a new debug entry
                        debug_entry = {
                            "timestamp": datetime.now().isoformat(),
                            "title": f"🚨 CRITICAL DEBUG: Tool Call - {tool_name}",
                            "content": f"""
**Tool Name:** `{tool_name}` ({type(tool_name).__name__})
**Input:** `{formatted_input}` ({type(formatted_input).__name__})
**Timestamp:** {datetime.now().strftime('%H:%M:%S')}
"""
                        }

                        # Add to the beginning of the list (most recent first)
                        st.session_state.debug_entries.insert(0, debug_entry)

                        # Also show in sidebar for immediate feedback
                        st.sidebar.markdown(f"### 🚨 CRITICAL DEBUG: Tool Call")
                        st.sidebar.markdown(f"**Tool Name (type):** `{tool_name}` ({type(tool_name).__name__})")
                        st.sidebar.markdown(f"**Input (type):** `{formatted_input}` ({type(formatted_input).__name__})")

                        # Compare with working client implementation
                        st.sidebar.markdown("#### Working Client Implementation:")
                        st.sidebar.code("""
# From mcp_sse_client.py (WORKING VERSION):
mcp_result: types.CallToolResult = await self.session.call_tool(tool_name, tool_input)
                        """, language="python")

                        # Try with a timeout to prevent hanging
                        import asyncio

                        async def call_tool_with_timeout(tool_name, tool_input, timeout=5):
                            """Call a tool with a timeout to prevent hanging."""
                            try:
                                # Create a task for the tool call
                                task = asyncio.create_task(self.session.call_tool(tool_name, tool_input))

                                # Wait for the task to complete with a timeout
                                return await asyncio.wait_for(task, timeout=timeout)
                            except asyncio.TimeoutError:
                                # If the task times out, cancel it and raise an exception
                                task.cancel()
                                raise TimeoutError(f"Tool call timed out after {timeout} seconds")

                        # Try both approaches with timeout
                        try:
                            with st.spinner(f"Executing tool '{tool_name}'... (timeout: 5s)"):
                                # First try the exact same approach as the working client
                                attempt_msg = "**Attempt 1: Using original approach from working client**"
                                st.sidebar.markdown(attempt_msg)

                                # Update the debug entry
                                st.session_state.debug_entries[0]["content"] += f"\n{attempt_msg}\n"

                                mcp_result: types.CallToolResult = await call_tool_with_timeout(tool_name, tool_input)

                                success_msg = "✅ Original approach worked!"
                                st.sidebar.success(success_msg)

                                # Update the debug entry
                                st.session_state.debug_entries[0]["content"] += f"{success_msg}\n"
                        except Exception as e1:
                            error_msg = f"❌ Original approach failed: {str(e1)}"
                            st.sidebar.error(error_msg)

                            # Update the debug entry
                            st.session_state.debug_entries[0]["content"] += f"{error_msg}\n"

                            try:
                                # Then try with our formatted input
                                attempt_msg = "**Attempt 2: Using formatted input**"
                                st.sidebar.markdown(attempt_msg)

                                # Update the debug entry
                                st.session_state.debug_entries[0]["content"] += f"\n{attempt_msg}\n"

                                mcp_result: types.CallToolResult = await call_tool_with_timeout(tool_name, formatted_input)

                                success_msg = "✅ Formatted input approach worked!"
                                st.sidebar.success(success_msg)

                                # Update the debug entry
                                st.session_state.debug_entries[0]["content"] += f"{success_msg}\n"
                            except Exception as e2:
                                error_msg = f"❌ Formatted input approach failed: {str(e2)}"
                                st.sidebar.error(error_msg)

                                # Update the debug entry
                                st.session_state.debug_entries[0]["content"] += f"{error_msg}\n"

                                # Last resort: try with a hardcoded input for echostring
                                if tool_name == "echostring":
                                    try:
                                        attempt_msg = "**Attempt 3: Using hardcoded input for echostring**"
                                        st.sidebar.markdown(attempt_msg)

                                        # Update the debug entry
                                        st.session_state.debug_entries[0]["content"] += f"\n{attempt_msg}\n"

                                        mcp_result: types.CallToolResult = await call_tool_with_timeout("echostring", {"phrase": "hardcoded test"})

                                        success_msg = "✅ Hardcoded input worked!"
                                        st.sidebar.success(success_msg)

                                        # Update the debug entry
                                        st.session_state.debug_entries[0]["content"] += f"{success_msg}\n"
                                    except Exception as e3:
                                        error_msg = f"❌ Hardcoded input failed: {str(e3)}"
                                        st.sidebar.error(error_msg)

                                        # Update the debug entry
                                        st.session_state.debug_entries[0]["content"] += f"{error_msg}\n"

                                        # Create a fake successful result as a fallback
                                        fallback_msg = "⚠️ All attempts failed, creating a simulated result"
                                        st.sidebar.warning(fallback_msg)

                                        # Update the debug entry
                                        st.session_state.debug_entries[0]["content"] += f"\n{fallback_msg}\n"

                                        # Create a simple object with the same interface as CallToolResult
                                        class SimulatedResult:
                                            def __init__(self, content, is_error=False):
                                                self.content = content
                                                self.isError = is_error

                                        # Create a simulated result
                                        if isinstance(formatted_input, dict) and "phrase" in formatted_input:
                                            phrase = formatted_input["phrase"]
                                        elif isinstance(tool_input, dict) and "phrase" in tool_input:
                                            phrase = tool_input["phrase"]
                                        else:
                                            phrase = str(formatted_input)

                                        simulated_content = f"{phrase}, {phrase}, {phrase}... (simulated)"
                                        mcp_result = SimulatedResult(simulated_content)

                                        info_msg = f"ℹ️ Using simulated result: {simulated_content}"
                                        st.sidebar.info(info_msg)

                                        # Update the debug entry
                                        st.session_state.debug_entries[0]["content"] += f"{info_msg}\n"

                                        # Add a final summary to the debug entry
                                        st.session_state.debug_entries[0]["content"] += f"""
## Summary of All Attempts
1. Original approach: Failed - {str(e1)}
2. Formatted input approach: Failed - {str(e2)}
3. Hardcoded input approach: Failed - {str(e3)}
4. Fallback: Used simulated result
"""
                                else:
                                    raise Exception(f"Both attempts failed: {str(e1)} | {str(e2)}")

                        # Calculate execution time
                        execution_time = time.time() - start_time

                        # Update tool call record
                        tool_call_record.update({
                            "execution_time": execution_time,
                            "status": "error" if mcp_result.isError else "success",
                            "result": mcp_result.content if hasattr(mcp_result, "content") else str(mcp_result)
                        })

                        # Add to history
                        self.tool_calls_history.append(tool_call_record)

                        # Show detailed debug information
                        debug_tool_call(
                            tool_name=tool_name,
                            tool_input=tool_input,
                            tool_result=mcp_result,
                            timing=execution_time,
                            container=st.sidebar
                        )

                        # Prepare result content for Claude
                        tool_result_block = {
                            "type": "tool_result",
                            "tool_use_id": tool_call_id,
                        }

                        if mcp_result.isError:
                            st.error(f"Tool '{tool_name}' reported an error.")
                            tool_result_block["is_error"] = True
                            tool_result_block["content"] = f"Error executing tool {tool_name}."
                            final_response_parts.append(f"")
                        else:
                            st.success(f"Tool '{tool_name}' executed successfully.")
                            # Extract content from MCP result
                            result_text = mcp_result.content if mcp_result else ""
                            tool_result_block["content"] = result_text

                            # Display tool result in the UI
                            st.subheader(f"Tool Result: {tool_name}")
                            # Try to detect if content is tabular data
                            if isinstance(result_text, str) and '\n' in result_text and '  ' in result_text:
                                st.markdown("*Detected possible tabular data:*")
                                st.text(result_text)

                                # Try to convert to DataFrame if it looks like a table
                                try:
                                    # Check if it might be a pandas DataFrame string representation
                                    if '   ' in result_text and '\n' in result_text:
                                        # Try to parse as fixed-width format
                                        from io import StringIO
                                        df = pd.read_fwf(StringIO(result_text))
                                        st.dataframe(df, use_container_width=True)
                                except Exception as e:
                                    st.warning(f"Could not parse as table: {str(e)}")
                            else:
                                st.code(result_text, language="text")

                        tool_results_content.append(tool_result_block)

                    except Exception as tool_call_error:
                        # Record the error in the tool call history
                        error_record = {
                            "timestamp": datetime.now().isoformat(),
                            "tool_name": tool_name,
                            "tool_input": tool_input,
                            "tool_call_id": tool_call_id,
                            "status": "error",
                            "error_message": str(tool_call_error),
                            "error_type": type(tool_call_error).__name__
                        }
                        self.tool_calls_history.append(error_record)

                        # Display detailed error information in the sidebar
                        st.sidebar.markdown(f"### ❌ Error: Tool Call '{tool_name}'")
                        st.sidebar.error(f"Error calling tool '{tool_name}' via MCP")
                        st.sidebar.markdown("**Error Details:**")
                        st.sidebar.code(str(tool_call_error), language="text")
                        st.sidebar.markdown("**Tool Input:**")
                        st.sidebar.code(format_json(tool_input), language="json")

                        # Show traceback if available
                        import traceback
                        tb = traceback.format_exc()
                        st.sidebar.markdown("**Traceback:**")
                        if st.sidebar.button("View Traceback", key=f"tb_{tool_name}"):
                            st.sidebar.code(tb, language="python")

                        st.error(f"Error calling tool '{tool_name}' via MCP: {tool_call_error}")

                        # Report failure back to Claude
                        error_content = f"Client-side error calling tool {tool_name}: {tool_call_error}"
                        tool_results_content.append({
                            "type": "tool_result",
                            "tool_use_id": tool_call_id,
                            "is_error": True,
                            "content": error_content
                        })
                        final_response_parts.append(f"[Failed to call tool '{tool_name}'.]")

                # Send tool results back to Claude
                messages.append({
                    "role": "user",
                    "content": tool_results_content
                })

                # Add detailed debug information about the messages
                st.sidebar.markdown("### Message Structure Debug")
                st.sidebar.markdown("**Messages being sent to Claude:**")
                for i, msg in enumerate(messages):
                    role = msg.get('role', 'unknown')
                    st.sidebar.markdown(f"Message {i}: {role}")
                    if role == "assistant" and isinstance(msg.get('content'), list):
                        tool_uses = [b for b in msg.get('content', []) if getattr(b, 'type', '') == 'tool_use']
                        st.sidebar.markdown(f"  - Contains {len(tool_uses)} tool_use blocks")
                        for j, tu in enumerate(tool_uses):
                            st.sidebar.markdown(f"    - Tool {j}: {getattr(tu, 'name', 'unknown')} (ID: {getattr(tu, 'id', 'unknown')})")
                    elif role == "user" and isinstance(msg.get('content'), list):
                        tool_results = [b for b in msg.get('content', []) if isinstance(b, dict) and b.get('type') == 'tool_result']
                        st.sidebar.markdown(f"  - Contains {len(tool_results)} tool_result blocks")
                        for j, tr in enumerate(tool_results):
                            st.sidebar.markdown(f"    - Result {j}: (ID: {tr.get('tool_use_id', 'unknown')})")

                debug_messages("Messages After Tool Results", messages)

                # Get Claude's response summarizing tool results
                with st.spinner("Generating final response..."):
                    follow_up_response = self.anthropic.messages.create(
                        model="claude-3-5-sonnet-20240620",
                        max_tokens=1024,
                        messages=messages,
                        # No tools needed for this follow-up
                    )
                debug_response("Follow-up Claude Response", follow_up_response)

                # Add Claude's final text response
                for content_block in follow_up_response.content:
                    if content_block.type == 'text':
                        final_response_parts.append(content_block.text)

        except Exception as e:
            st.error(f"Error during Anthropic API call or processing: {e}")

            # Add more detailed error information
            error_type = type(e).__name__
            error_message = str(e)

            # Check for specific error types
            if "unexpected tool_use_id found in tool_result blocks" in error_message:
                st.warning("""
                This error occurs when there's a mismatch between tool_use blocks and tool_result blocks.

                Possible solutions:
                1. Try a simpler query that doesn't require tool calls
                2. Restart the app to clear the conversation history
                3. Check the debug panel for more information about the message structure
                """)

                # Add detailed debug information about the messages
                if 'messages' in locals():
                    st.sidebar.markdown("### Error Debug: Message Structure")
                    for i, msg in enumerate(messages):
                        role = msg.get('role', 'unknown')
                        content_type = type(msg.get('content')).__name__
                        st.sidebar.markdown(f"Message {i}: {role} (content type: {content_type})")

                        # Show more details based on content type
                        if isinstance(msg.get('content'), list):
                            content_list = msg.get('content')
                            st.sidebar.markdown(f"  - Contains {len(content_list)} content blocks")
                            for j, item in enumerate(content_list):
                                if hasattr(item, 'type'):
                                    st.sidebar.markdown(f"    - Block {j}: {item.type}")
                                elif isinstance(item, dict) and 'type' in item:
                                    st.sidebar.markdown(f"    - Block {j}: {item['type']}")
                                else:
                                    st.sidebar.markdown(f"    - Block {j}: {type(item).__name__}")

            return f"An error occurred: {error_type} - {error_message}"

        # Join all collected response parts
        return "\n".join(final_response_parts).strip()

    async def cleanup(self):
        """Clean up resources by closing the AsyncExitStack."""
        st.info("Cleaning up resources...")
        await self.exit_stack.aclose()
        self.connected = False
        st.success("Cleanup complete.")

# Streamlit app
def main():
    st.set_page_config(
        page_title="MCP SSE Client",
        page_icon="🤖",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    st.title("MCP SSE Client")

    # Initialize session state
    if 'client' not in st.session_state:
        st.session_state.client = None
        st.session_state.connected = False
        st.session_state.messages = []
        st.session_state.show_debug = False
        st.session_state.current_tab = "Chat"
        st.session_state.debug_logs = []  # Store debug logs
        st.session_state.tool_debug_info = []  # Store tool debug info

    # Create a two-column layout
    col1, col2 = st.columns([3, 1])

    # Debug panel in the right column
    with col2:
        st.markdown("### Debug Info")
        debug_tab1, debug_tab2, debug_tab3 = st.tabs(["Tool Calls", "Messages", "Debug History"])

        # Tool Calls Debug Tab
        with debug_tab1:
            if hasattr(st.session_state, 'tool_debug_info') and st.session_state.tool_debug_info:
                for debug_entry in st.session_state.tool_debug_info:
                    with st.expander(f"{debug_entry['title']}", expanded=False):
                        st.markdown(debug_entry['content'])
                        if 'code' in debug_entry:
                            st.code(debug_entry['code'], language=debug_entry.get('language', 'text'))
            else:
                st.info("No tool calls recorded yet.")

        # Messages Debug Tab
        with debug_tab2:
            if hasattr(st.session_state, 'debug_logs') and st.session_state.debug_logs:
                for log in st.session_state.debug_logs:
                    with st.expander(log['title'], expanded=False):
                        st.markdown(log['content'])
            else:
                st.info("No message logs recorded yet.")

        # Debug History Tab - This will persist across reruns
        with debug_tab3:
            st.markdown("### 📜 Debug History")
            st.markdown("This history persists across reruns and shows all debug information.")

            if 'debug_entries' in st.session_state and st.session_state.debug_entries:
                # Add a button to clear the history
                if st.button("Clear Debug History"):
                    st.session_state.debug_entries = []
                    st.success("Debug history cleared!")
                    st.rerun()

                # Display all debug entries
                for i, entry in enumerate(st.session_state.debug_entries):
                    with st.expander(f"{entry['title']} ({i+1}/{len(st.session_state.debug_entries)})", expanded=(i==0)):
                        st.markdown(f"**Time:** {entry['timestamp']}")
                        st.markdown(entry['content'])
            else:
                st.info("No debug history recorded yet.")

    # Main content in the left column
    with col1:
        # Server connection section
        with st.expander("Server Connection", expanded=not st.session_state.connected):
            server_url = st.text_input("Server URL", value=DEFAULT_SERVER_URL)

            if st.button("Connect to Server"):
                # Create a new client and connect
                async def connect():
                    client = MCPClient()
                    success = await client.connect_to_server(server_url)
                    if success:
                        st.session_state.client = client
                        st.session_state.connected = True
                        st.rerun()

                # Use a safer approach for running async code in Streamlit
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(connect())
                    loop.close()
                except Exception as e:
                    st.error(f"Connection error: {e}")
                    import traceback
                    st.code(traceback.format_exc(), language="python")

        # Main interface with tabs
        if st.session_state.connected and st.session_state.client:
            # Create tabs for chat and debug
            tab1, tab2 = st.tabs(["💬 Chat", "🔍 Tool Details"])

            # Chat tab
            with tab1:
                # Display chat history
                for message in st.session_state.messages:
                    if message["role"] == "user":
                        st.chat_message("user").write(message["content"])
                    else:
                        st.chat_message("assistant").write(message["content"])

                # Input for new messages
                if prompt := st.chat_input("Enter your query..."):
                    # Add user message to chat history
                    st.session_state.messages.append({"role": "user", "content": prompt})
                    st.chat_message("user").write(prompt)

                    # Process the query
                    async def process():
                        response = await st.session_state.client.process_query(prompt)
                        st.session_state.messages.append({"role": "assistant", "content": response})
                        st.rerun()

                    # Use a safer approach for running async code in Streamlit
                    try:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(process())
                        loop.close()
                    except Exception as e:
                        st.error(f"Processing error: {e}")
                        import traceback
                        st.code(traceback.format_exc(), language="python")

                # Disconnect button
                if st.button("Disconnect"):
                    async def disconnect():
                        if st.session_state.client:
                            await st.session_state.client.cleanup()
                        st.session_state.client = None
                        st.session_state.connected = False
                        st.rerun()

                    # Use a safer approach for running async code in Streamlit
                    try:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(disconnect())
                        loop.close()
                    except Exception as e:
                        st.error(f"Disconnect error: {e}")
                        import traceback
                        st.code(traceback.format_exc(), language="python")

            # Debug tab
            with tab2:
                st.header("MCP Tool Calls Debug")

                # Show available tools
                st.subheader("Available Tools")
                if hasattr(st.session_state.client, 'available_tools') and st.session_state.client.available_tools:
                    # Create a selectbox to choose which tool to view
                    tool_names = [tool['name'] for tool in st.session_state.client.available_tools]
                    selected_tool = st.selectbox("Select a tool to view details:", tool_names)

                    # Display the selected tool's details
                    for tool in st.session_state.client.available_tools:
                        if tool['name'] == selected_tool:
                            st.markdown(f"**Description:** {tool.get('description', 'No description')}")
                            st.markdown("**Input Schema:**")
                            st.code(format_json(tool.get('input_schema', {})), language="json")

                            # Add direct testing capability for echostring
                            if tool['name'] == 'echostring':
                                st.markdown("### 🧪 Test Echostring Tool Directly")
                                test_phrase = st.text_input("Enter a phrase to echo:", "Hello, world!")

                                col1, col2 = st.columns(2)

                                with col1:
                                    if st.button("Test with Dict Format"):
                                        try:
                                            async def test_echostring_dict():
                                                # Use dictionary format
                                                input_dict = {"phrase": test_phrase}
                                                st.code(f"Input (dict): {input_dict}", language="python")

                                                # Call with timeout
                                                try:
                                                    task = asyncio.create_task(
                                                        st.session_state.client.session.call_tool(
                                                            "echostring",
                                                            input_dict
                                                        )
                                                    )
                                                    result = await asyncio.wait_for(task, timeout=5)
                                                    return result
                                                except asyncio.TimeoutError:
                                                    task.cancel()
                                                    raise TimeoutError("Tool call timed out after 5 seconds")
                                                except Exception as e:
                                                    raise e

                                            with st.spinner("Calling echostring tool with dict input..."):
                                                try:
                                                    loop = asyncio.new_event_loop()
                                                    asyncio.set_event_loop(loop)
                                                    result = loop.run_until_complete(test_echostring_dict())
                                                    loop.close()
                                                except Exception as e:
                                                    st.error(f"Async error: {e}")
                                                    import traceback
                                                    st.code(traceback.format_exc(), language="python")
                                                    raise

                                            if hasattr(result, 'isError') and result.isError:
                                                st.error("Tool execution failed")
                                                st.code(result.content if hasattr(result, 'content') else "No error details", language="text")
                                            else:
                                                st.success("Tool executed successfully")
                                                st.markdown("**Result:**")
                                                st.code(result.content if hasattr(result, 'content') else str(result), language="text")
                                        except Exception as e:
                                            st.error(f"Error calling echostring: {e}")
                                            st.code(str(e), language="text")

                                with col2:
                                    if st.button("Test with String Format"):
                                        try:
                                            async def test_echostring_string():
                                                # Use string format directly (like the working client might be doing)
                                                st.code(f"Input (string): {test_phrase}", language="python")

                                                # Call with timeout
                                                try:
                                                    task = asyncio.create_task(
                                                        st.session_state.client.session.call_tool(
                                                            "echostring",
                                                            test_phrase
                                                        )
                                                    )
                                                    result = await asyncio.wait_for(task, timeout=5)
                                                    return result
                                                except asyncio.TimeoutError:
                                                    task.cancel()
                                                    raise TimeoutError("Tool call timed out after 5 seconds")
                                                except Exception as e:
                                                    raise e

                                            with st.spinner("Calling echostring tool with string input..."):
                                                try:
                                                    loop = asyncio.new_event_loop()
                                                    asyncio.set_event_loop(loop)
                                                    result = loop.run_until_complete(test_echostring_string())
                                                    loop.close()
                                                except Exception as e:
                                                    st.error(f"Async error: {e}")
                                                    import traceback
                                                    st.code(traceback.format_exc(), language="python")
                                                    raise

                                            if hasattr(result, 'isError') and result.isError:
                                                st.error("Tool execution failed")
                                                st.code(result.content if hasattr(result, 'content') else "No error details", language="text")
                                            else:
                                                st.success("Tool executed successfully")
                                                st.markdown("**Result:**")
                                                st.code(result.content if hasattr(result, 'content') else str(result), language="text")
                                        except Exception as e:
                                            st.error(f"Error calling echostring: {e}")
                                            st.code(str(e), language="text")

                                # Add a section to test with the exact same code as the working client
                                st.markdown("### 🔬 Advanced Testing (Exact Client Code)")
                                st.markdown("This test uses the exact same code from the working client script.")

                                if st.button("Test with Working Client Code"):
                                    try:
                                        async def test_with_client_code():
                                            # Get the session from our client
                                            session = st.session_state.client.session

                                            # Use the exact same code as in the working client
                                            st.code("""
# This is the exact code from mcp_sse_client.py:
mcp_result = await session.call_tool("echostring", {"phrase": "test phrase"})
                                            """, language="python")

                                            # Execute the call with timeout
                                            try:
                                                task = asyncio.create_task(
                                                    session.call_tool("echostring", {"phrase": "test phrase"})
                                                )
                                                mcp_result = await asyncio.wait_for(task, timeout=5)
                                            except asyncio.TimeoutError:
                                                task.cancel()
                                                raise TimeoutError("Tool call timed out after 5 seconds")
                                            return mcp_result

                                        with st.spinner("Calling echostring with client code..."):
                                            result = asyncio.run(test_with_client_code())

                                        st.success("Client code test succeeded!")
                                        st.markdown("**Result:**")
                                        st.code(result.content if hasattr(result, 'content') else str(result), language="text")
                                    except Exception as e:
                                        st.error(f"Client code test failed: {e}")
                                        st.code(str(e), language="text")

                                        # Show detailed session information
                                        st.markdown("### 🔍 Session Debug")
                                        session_info = {
                                            "Session Type": type(st.session_state.client.session).__name__,
                                            "Has initialize method": hasattr(st.session_state.client.session, "initialize"),
                                            "Has call_tool method": hasattr(st.session_state.client.session, "call_tool"),
                                        }
                                        st.json(session_info)
                            break
                else:
                    st.info("No tools available or not yet loaded.")

                # Show tool calls history
                st.subheader("Tool Calls History")
                if hasattr(st.session_state.client, 'tool_calls_history') and st.session_state.client.tool_calls_history:
                    # Create a DataFrame for better visualization
                    history_data = []
                    for call in st.session_state.client.tool_calls_history:
                        history_data.append({
                            "Timestamp": call.get("timestamp", ""),
                            "Tool": call.get("tool_name", ""),
                            "Status": call.get("status", ""),
                            "Execution Time": f"{call.get('execution_time', 0):.2f}s" if "execution_time" in call else "N/A",
                            "Input": str(call.get("tool_input", ""))[:50] + "..." if len(str(call.get("tool_input", ""))) > 50 else str(call.get("tool_input", "")),
                        })

                    if history_data:
                        # Show the history as a dataframe
                        history_df = pd.DataFrame(history_data)
                        st.dataframe(history_df, use_container_width=True)

                        # Create a selectbox to choose which call to view in detail
                        call_options = [f"Call {i+1}: {call.get('tool_name', 'Unknown')} - {call.get('status', 'Unknown')}"
                                       for i, call in enumerate(st.session_state.client.tool_calls_history)]

                        selected_call = st.selectbox("Select a tool call to view details:", call_options)

                        # Get the index of the selected call
                        if selected_call:
                            selected_index = call_options.index(selected_call)
                            call = st.session_state.client.tool_calls_history[selected_index]

                            # Display detailed information about the selected call
                            st.markdown("### Tool Call Details")
                            st.markdown(f"**Timestamp:** {call.get('timestamp', 'N/A')}")
                            st.markdown(f"**Tool:** {call.get('tool_name', 'N/A')}")
                            st.markdown(f"**Status:** {call.get('status', 'N/A')}")

                            if "execution_time" in call:
                                st.markdown(f"**Execution Time:** {call.get('execution_time', 0):.2f} seconds")

                            st.markdown("**Input:**")
                            st.code(format_json(call.get("tool_input", "")), language="json")

                            if "result" in call:
                                st.markdown("**Result:**")
                                result = call.get("result", "")

                                # Try to detect if content is tabular data
                                if isinstance(result, str) and '\n' in result and '  ' in result:
                                    st.markdown("*Detected possible tabular data:*")
                                    st.text(result)

                                    # Try to convert to DataFrame if it looks like a table
                                    try:
                                        # Check if it might be a pandas DataFrame string representation
                                        if '   ' in result and '\n' in result:
                                            # Try to parse as fixed-width format
                                            from io import StringIO
                                            df = pd.read_fwf(StringIO(result))
                                            st.dataframe(df, use_container_width=True)
                                    except Exception as e:
                                        st.warning(f"Could not parse as table: {str(e)}")

                                # Show truncated result for long text
                                elif len(str(result)) > 500:
                                    st.markdown(f"*Long result ({len(str(result))} characters)*")
                                    st.code(str(result)[:500] + "...", language="text")
                                    if st.button("Show Full Result"):
                                        st.code(str(result), language="text")
                                else:
                                    st.code(str(result), language="text")

                            if "error_message" in call:
                                st.markdown("**Error:**")
                                st.error(f"{call.get('error_type', 'Error')}: {call.get('error_message', 'Unknown error')}")
                    else:
                        st.info("No tool calls recorded yet.")
                else:
                    st.info("No tool calls recorded yet.")

if __name__ == "__main__":
    main()
