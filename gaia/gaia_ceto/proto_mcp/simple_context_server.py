#!/usr/bin/env python3
"""
Simple FastMCP server to test Context injection without dependencies
"""

import asyncio
import time
import json
from fastmcp import FastMCP, Context
from pydantic import Field
import argparse
import uvicorn

# Create FastMCP server
mcp = FastMCP("SimpleContextServer")

@mcp.tool()
async def simple_process_items(
    phrase: str = Field(description="Base phrase to process"),
    num_items: int = Field(description="Number of items to process (default 5)", default=5),
    ctx: Context | None = None  # FastMCP Context parameter for progress reporting
) -> str:
    """Process a list of items with progress updates using FastMCP Context
    
    This is a simplified version of echostring_process_items that tests
    FastMCP Context injection without external dependencies.
    """
    start_time = time.time()
    
    # Create items to process based on the phrase
    items = []
    for idx in range(num_items):
        if idx % 4 == 0:
            items.append(f"{phrase}_{idx}")
        elif idx % 4 == 1:
            items.append(f"{phrase}_variant_{idx}")
        elif idx % 4 == 2:
            items.append(f"processed_{phrase}_{idx}")
        else:
            items.append(f"{phrase}_final_{idx}")
    
    total = len(items)
    results = []
    progress_log = []
    
    # Test Context injection
    if ctx:
        try:
            await ctx.report_progress(progress=0, total=total)
            await ctx.info(f"✅ FastMCP Context: Started processing {total} items based on '{phrase}'")
            progress_log.append(f"[{time.strftime('%H:%M:%S')}] ✅ FastMCP Context: Started processing {total} items")
        except Exception as e:
            progress_log.append(f"[{time.strftime('%H:%M:%S')}] ⚠️ Context error: {e}")
            print(f"🔄 FALLBACK: Started processing {total} items based on '{phrase}'", flush=True)
    else:
        progress_log.append(f"[{time.strftime('%H:%M:%S')}] ❌ No FastMCP context received")
        print(f"🔄 PROGRESS: Started processing {total} items based on '{phrase}'", flush=True)
    
    # Process each item with progress updates
    for i, item in enumerate(items):
        # Report progress using FastMCP Context
        if ctx:
            try:
                await ctx.report_progress(progress=i, total=total)
                await ctx.info(f"Processing item {i+1}/{total}: {item}")
                progress_log.append(f"[{time.strftime('%H:%M:%S')}] ✅ FastMCP: Processing {i+1}/{total}: {item}")
            except Exception as e:
                progress_log.append(f"[{time.strftime('%H:%M:%S')}] ⚠️ Context error: {e}")
                print(f"🔄 FALLBACK: {i+1}/{total} - Processing: {item}", flush=True)
        else:
            progress_log.append(f"[{time.strftime('%H:%M:%S')}] 📊 Progress: {i+1}/{total} - Processing: {item}")
            print(f"🔄 PROGRESS: {i+1}/{total} - Processing: {item}", flush=True)
        
        # Simulate processing work
        await asyncio.sleep(0.5)  # Simulate processing time
        
        # Process the item (convert to uppercase and add metadata)
        processed_item = {
            "original": item,
            "processed": item.upper(),
            "timestamp": time.strftime('%H:%M:%S'),
            "index": i,
            "based_on_phrase": phrase
        }
        results.append(processed_item)
        
        # Log completion of this item
        progress_log.append(f"[{time.strftime('%H:%M:%S')}] ✅ Completed: {item} → {item.upper()}")
    
    # Report final completion using FastMCP Context
    if ctx:
        try:
            await ctx.report_progress(progress=total, total=total)
            await ctx.info(f"🎉 All {total} items processed successfully")
            progress_log.append(f"[{time.strftime('%H:%M:%S')}] ✅ FastMCP Context: All {total} items completed")
        except Exception as e:
            progress_log.append(f"[{time.strftime('%H:%M:%S')}] ⚠️ Context error: {e}")
            print(f"🔄 FALLBACK: Processing complete: {total}/{total} items", flush=True)
    else:
        progress_log.append(f"[{time.strftime('%H:%M:%S')}] 🎉 Processing complete: {total}/{total} items")
        print(f"🔄 PROGRESS: Processing complete: {total}/{total} items", flush=True)
    
    # Calculate final metrics
    end_time = time.time()
    total_duration = end_time - start_time
    
    # Create result data structure
    result_data = {
        "type": "processing_results",
        "title": f"Processing Results for: {phrase}",
        "summary": {
            "base_phrase": phrase,
            "total_items": total,
            "processed_count": len(results),
            "duration_seconds": round(total_duration, 2),
            "items_per_second": round(len(results) / total_duration, 2),
            "context_status": "✅ Context received" if ctx else "❌ No context"
        },
        "progress_log": progress_log,
        "results": results,
        "timestamp": time.strftime('%H:%M:%S')
    }
    
    return json.dumps(result_data, indent=2)

@mcp.tool()
def simple_echo(message: str = Field(description="Message to echo")) -> str:
    """Simple echo tool without context"""
    return f"Echo: {message}"

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--port", type=int, default=9002, help="HTTP port")
    args = parser.parse_args()

    print(f"🚀 Starting Simple Context Server on port {args.port}...")
    print("📋 Tools available:")
    print("  - simple_process_items: Test Context injection with progress reporting")
    print("  - simple_echo: Simple echo tool without context")
    
    # Create the app using http_app (recommended for FastMCP 2.5.1)
    app = mcp.http_app()

    uvicorn.run(app, host="0.0.0.0", port=args.port, log_level="info")
