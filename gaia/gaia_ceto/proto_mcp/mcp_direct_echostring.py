#!/usr/bin/env python3
"""
Direct Echostring Client

A no-nonsense, direct implementation that just works.
"""

import asyncio
import json
import time
from datetime import datetime
import sys
import os

# Add the current directory to the path to ensure imports work
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the client library
from mcp_sse_clientlib import MCPClientLib

# Default server URL
DEFAULT_SERVER_URL = "http://0.0.0.0:8000/sse"

def debug_callback(level, message, data=None):
    """Simple debug callback that prints to console."""
    print(f"[{level.upper()}] {message}")
    if data and level == "debug":
        print(f"  Data: {data}")
    return None

async def echo_string(phrase, server_url=DEFAULT_SERVER_URL, timeout=30):
    """
    Direct function to echo a string using the echostring tool.
    
    Args:
        phrase: The string to echo
        server_url: The server URL (default: http://0.0.0.0:8000/sse)
        timeout: Timeout in seconds (default: 30)
        
    Returns:
        The echoed string or error message
    """
    print(f"Connecting to server at {server_url}")
    print(f"Will echo: '{phrase}'")
    
    # Create client
    client = MCPClientLib(debug_callback=debug_callback)
    
    try:
        # Connect to server
        print("Establishing connection...")
        success = await client.connect_to_server(server_url)
        
        if not success:
            return "Failed to connect to server."
        
        print("Connected successfully.")
        print(f"Available tools: {', '.join([tool['name'] for tool in client.available_tools])}")
        
        # Call the echostring tool directly
        print(f"Calling echostring with timeout={timeout}s...")
        start_time = time.time()
        
        result = await client.call_tool(
            tool_name="echostring",
            tool_input={"phrase": phrase},
            tool_call_id="direct_call",
            timeout=timeout
        )
        
        elapsed = time.time() - start_time
        print(f"Call completed in {elapsed:.2f} seconds")
        
        if result.success:
            print("Success!")
            return f"Result: {result.content}"
        else:
            print(f"Error: {result.error}")
            return f"Error: {result.error}"
            
    except Exception as e:
        print(f"Exception: {e}")
        import traceback
        traceback.print_exc()
        return f"Exception: {e}"
    finally:
        # Clean up
        print("Cleaning up...")
        await client.cleanup()
        print("Done.")

def main():
    """Main entry point."""
    # Get phrase from command line or use default
    phrase = sys.argv[1] if len(sys.argv) > 1 else "Hello, world!"
    
    # Get server URL from command line or use default
    server_url = sys.argv[2] if len(sys.argv) > 2 else DEFAULT_SERVER_URL
    
    # Run the async function
    result = asyncio.run(echo_string(phrase, server_url))
    
    # Print the result
    print("\nFinal result:")
    print(result)

if __name__ == "__main__":
    main()
