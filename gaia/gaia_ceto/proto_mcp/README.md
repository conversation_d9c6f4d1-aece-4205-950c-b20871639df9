# Usage
- load server: python mcp_sse_server.py
- then run terminal client: mcp_sse_client_light.py
- library: mcp_sse_clientlib.py
- streamlit: (broken) mcp_sse_streamlit_v2.py

Currently only anthropic (mcp support)
Terminal client is gold standard.
Streamlit client semi-works but cant call tools, Augment couldnt solve it.  Think has to do with conflicting event loops.

Maybe look at streaming http instead of sse
