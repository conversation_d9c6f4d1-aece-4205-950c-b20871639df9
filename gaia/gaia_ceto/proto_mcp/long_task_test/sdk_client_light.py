# mcp_client_min.py
import asyncio
from contextlib import AsyncExitStack

from mcp import ClientSession                # core session object
from mcp.client.sse import sse_client        # SSE transport helper


async def main() -> None:
    async with AsyncExitStack() as stack:
        # 1️⃣  open the SSE connection to the server
        read_stream, write_stream = await stack.enter_async_context(
            sse_client("http://127.0.0.1:9000/sse")   # <- your server URL
        )

        # 2️⃣  bind an MCP session to that transport
        session = await stack.enter_async_context(
            ClientSession(read_stream, write_stream)
        )
        await session.initialize()                    # handshake

        # 3️⃣  (optional) discover what tools the server exposes
        tools = await session.list_tools()
        print('tools', tools)
        #print("Tools on server:", [t.name for t in tools])

        # 4️⃣  call the long-running tool
        result = await session.call_tool("long_task", {})   # no args
        print("Final result from server:", result.content)

        # NOTE: result.content is a list of Content objects.  For a simple
        #       text payload you can do `result.content[0].text`.

if __name__ == "__main__":
    asyncio.run(main())
