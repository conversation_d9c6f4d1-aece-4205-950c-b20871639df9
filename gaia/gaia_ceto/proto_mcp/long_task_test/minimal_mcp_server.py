#from fastmcp import <PERSON><PERSON><PERSON>, Context
from mcp.server.fastmcp import FastMCP, Context
import asyncio

mcp = FastMCP("ProgressServer")

@mcp.tool()
async def long_task(ctx: Context = None):
    """Long running task with progress reporting."""
    print(f"🔍 DEBUG: long_task called with ctx = {ctx}, type = {type(ctx)}")

    if ctx:
        try:
            await ctx.info("//// CONTEXT LONG TASK INITIAL - Starting long task with 5 steps... ////")
        except Exception as e:
            print(f"🔍 DEBUG: ctx.info() failed: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ No context injected")

    for i in range(5):
        await asyncio.sleep(1)
        print(f'🔄 PROGRESS: Step {i + 1}/5 completed')

        if ctx:
            try:
                await ctx.report_progress(i + 1, 5, '//// SERVER-SIDE CUSTOM MESSAGE ////')
            except Exception as e:
                print(f"🔍 DEBUG: ctx.report_progress() failed: {e}")
                import traceback
                traceback.print_exc()

            try:
                await ctx.info(f"//// CONTEXT INFO STEP {i + 1} - ✅ Completed step {i + 1}/5 ////")
            except Exception as e:
                print(f"🔍 DEBUG: ctx.info() failed: {e}")

            # Simple additional ctx.info() call
            try:
                await ctx.info(f"//// ADDITIONAL INFO {i + 1} - Extra info message ////")
            except Exception as e:
                print(f"🔍 DEBUG: Additional ctx.info() failed: {e}")
        else:
            print(f'📊 !!! No context - Step {i + 1}/5')

    if ctx:
        await ctx.info("🎉 Long task completed successfully!")

    return {"status": "completed", "steps": 5, "message": "Long task finished"}

if __name__ == "__main__":
    # Use uvicorn directly for more control
    import uvicorn

    # Create the SSE app
    app = mcp.sse_app()

    # Run with uvicorn
    uvicorn.run(app, host="127.0.0.1", port=9000, log_level="info")
