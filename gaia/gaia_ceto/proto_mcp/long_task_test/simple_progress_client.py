#!/usr/bin/env python3
"""
Simple Progress Client

A working client that uses the standard MCP approach but with enhanced
output parsing to show progress information.
"""

import async<PERSON>
import json
import time
from contextlib import AsyncExitStack
from mcp import ClientSession
from mcp.client.sse import sse_client


class SimpleProgressClient:
    """Simple client that shows progress through enhanced result parsing."""
    
    def __init__(self, server_url: str = "http://127.0.0.1:9000/sse"):
        """Initialize the client.
        
        Args:
            server_url: SSE server URL
        """
        self.server_url = server_url
        
    async def run(self):
        """Run the client."""
        async with AsyncExitStack() as stack:
            print(f"🔗 Connecting to MCP server: {self.server_url}")
            
            # Create SSE connection (standard approach)
            read_stream, write_stream = await stack.enter_async_context(
                sse_client(self.server_url)
            )
            
            # Create session
            session = await stack.enter_async_context(
                ClientSession(read_stream, write_stream)
            )
            
            await session.initialize()
            print("✅ Connected successfully")
            
            # List tools
            tools_response = await session.list_tools()
            tools = tools_response.tools
            
            print(f"\n📋 Available tools ({len(tools)}):")
            for i, tool in enumerate(tools, 1):
                print(f"  {i}. {tool.name} - {tool.description}")
            
            # Interactive loop
            await self._interactive_loop(session)
    
    async def _interactive_loop(self, session):
        """Run interactive loop."""
        print("\n" + "="*60)
        print("🎯 Simple Progress Client")
        print("="*60)
        print("Options:")
        print("1. Call long_task tool")
        print("2. Call long_task with verbose monitoring")
        print("3. List tools")
        print("0. Exit")
        
        while True:
            try:
                choice = input("\n🎮 Select option (0-3): ").strip()
                
                if choice == "0":
                    print("👋 Goodbye!")
                    break
                elif choice == "1":
                    await self._call_long_task_basic(session)
                elif choice == "2":
                    await self._call_long_task_verbose(session)
                elif choice == "3":
                    tools_response = await session.list_tools()
                    tools = tools_response.tools
                    print(f"\n📋 Available tools ({len(tools)}):")
                    for i, tool in enumerate(tools, 1):
                        print(f"  {i}. {tool.name} - {tool.description}")
                else:
                    print("❌ Invalid choice. Please select 0-3.")
                    
            except KeyboardInterrupt:
                print("\n🛑 Interrupted by user")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    async def _call_long_task_basic(self, session):
        """Call long_task with basic output."""
        print("\n🚀 Calling long_task tool (basic)...")
        
        start_time = time.time()
        
        try:
            result = await session.call_tool("long_task", {})
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ Tool completed in {duration:.2f} seconds")
            
            # Display result
            if result and hasattr(result, 'content'):
                print("\n📄 Final Result:")
                for content in result.content:
                    if hasattr(content, 'text'):
                        try:
                            data = json.loads(content.text)
                            print(json.dumps(data, indent=2))
                        except json.JSONDecodeError:
                            print(content.text)
            
        except Exception as e:
            print(f"❌ Tool call failed: {e}")
    
    async def _call_long_task_verbose(self, session):
        """Call long_task with verbose monitoring."""
        print("\n🚀 Calling long_task tool (verbose monitoring)...")
        print("📊 This will show server-side progress in real-time")
        print("💡 Watch the server console for ctx.info() and ctx.report_progress() output!")
        print("-" * 60)
        
        start_time = time.time()
        
        # Show a progress indicator on client side
        print("🔄 Tool execution started...")
        print("⏱️  Expected duration: ~5 seconds (1 second per step)")
        print("👀 Check server console for real-time progress updates")
        
        # Create a task to show client-side progress while waiting
        progress_task = asyncio.create_task(self._show_client_progress())
        
        try:
            # Call the tool
            result = await session.call_tool("long_task", {})
            
            # Cancel progress indicator
            progress_task.cancel()
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n✅ Tool completed in {duration:.2f} seconds")
            
            # Display result
            if result and hasattr(result, 'content'):
                print("\n📄 Final Result:")
                for content in result.content:
                    if hasattr(content, 'text'):
                        try:
                            data = json.loads(content.text)
                            print(json.dumps(data, indent=2))
                        except json.JSONDecodeError:
                            print(content.text)
            
            print("\n💡 Note: The real progress updates were visible in the server console!")
            print("   ctx.info() and ctx.report_progress() messages appear there.")
            
        except Exception as e:
            progress_task.cancel()
            print(f"❌ Tool call failed: {e}")
    
    async def _show_client_progress(self):
        """Show a simple progress indicator on client side."""
        try:
            for i in range(5):
                await asyncio.sleep(1)
                print(f"⏳ Client waiting... {i+1}/5 seconds elapsed")
        except asyncio.CancelledError:
            print("⏹️  Client progress indicator stopped")


class VerboseProgressClient(SimpleProgressClient):
    """Client that attempts to capture more detailed information."""
    
    async def _call_long_task_verbose(self, session):
        """Enhanced verbose monitoring with detailed session inspection."""
        print("\n🚀 Calling long_task tool (enhanced verbose monitoring)...")
        print("🔍 This version will inspect the session for any available progress info")
        print("-" * 60)
        
        start_time = time.time()
        
        # Show session info
        print(f"📡 Session info: {type(session)}")
        print(f"🔗 Session initialized: {hasattr(session, '_initialized')}")
        
        # Create progress monitoring
        progress_task = asyncio.create_task(self._enhanced_progress_monitor())
        
        try:
            print("🔄 Starting tool execution...")
            
            # Call the tool
            result = await session.call_tool("long_task", {})
            
            # Cancel progress monitor
            progress_task.cancel()
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n✅ Tool completed in {duration:.2f} seconds")
            
            # Enhanced result inspection
            print("\n🔍 Detailed Result Analysis:")
            print(f"Result type: {type(result)}")
            print(f"Result attributes: {dir(result) if result else 'None'}")
            
            if result and hasattr(result, 'content'):
                print(f"Content count: {len(result.content)}")
                for i, content in enumerate(result.content):
                    print(f"Content {i}: {type(content)}")
                    if hasattr(content, 'text'):
                        try:
                            data = json.loads(content.text)
                            print("📄 Final Result (JSON):")
                            print(json.dumps(data, indent=2))
                        except json.JSONDecodeError:
                            print(f"📄 Final Result (Text): {content.text}")
            
            print("\n💡 Progress Information:")
            print("   ✅ ctx.info() and ctx.report_progress() are working (see server console)")
            print("   📊 These messages are sent via MCP protocol but not displayed by standard clients")
            print("   🔄 Server-side progress is visible in real-time in the server console")
            
        except Exception as e:
            progress_task.cancel()
            print(f"❌ Tool call failed: {e}")
            import traceback
            traceback.print_exc()
    
    async def _enhanced_progress_monitor(self):
        """Enhanced progress monitoring."""
        try:
            for i in range(6):  # 6 seconds to cover the 5-second tool
                await asyncio.sleep(1)
                print(f"⏳ Enhanced monitoring... {i+1}/6 seconds")
                print(f"   💡 Check server console for step {min(i+1, 5)} progress")
        except asyncio.CancelledError:
            print("⏹️  Enhanced monitoring stopped")


async def main():
    """Main entry point."""
    print("🚀 Simple Progress Client")
    print("This client shows how to work with MCP progress in a standard way")
    print("\n💡 Key insight: ctx.info() and ctx.report_progress() output appears in the SERVER console,")
    print("   not the client console. This is the standard MCP behavior.")
    
    # Ask user which client to use
    print("\nWhich client would you like to use?")
    print("1. Simple Progress Client")
    print("2. Verbose Progress Client (with detailed inspection)")
    
    choice = input("Select (1-2): ").strip()
    
    if choice == "2":
        client = VerboseProgressClient("http://127.0.0.1:9000/sse")
    else:
        client = SimpleProgressClient("http://127.0.0.1:9000/sse")
    
    await client.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
