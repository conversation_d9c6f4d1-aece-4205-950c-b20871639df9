# fastmcp_client_with_progress_and_logs.py
import asyncio
from fastmcp import Client
from fastmcp.client.logging import LogMessage           # <- NEW


PROTOCOL = 'http' # http or sse


# ────────────────────────────────
# Progress updates  (ctx.report_progress)
# ────────────────────────────────
async def cli_progress(progress: float,
                       total: float | None,
                       message: str | None) -> None:
    """Print every progress notification on its own line."""
    if total:
        pct = progress / total * 100
        print(f"⏳ {pct:5.1f}%  {message or ''}", flush=True)
    else:
        print(f"⏳ step {progress}  {message or ''}", flush=True)


# ────────────────────────────────
# Log messages  (ctx.info / ctx.debug / …)
# ────────────────────────────────
async def cli_log(message: LogMessage) -> None:
    """
    Handle server-side logging.

    FastMCP wraps each call to ctx.info(), ctx.debug(), etc. in a
    LogMessage object and sends it as a `notifications/log` event.
    """
    level   = message.level.upper()       # e.g. INFO, ERROR
    logger  = message.logger or "server"
    payload = message.data                # whatever string/object you passed
    print(f"[{level}] {logger}: {payload}", flush=True)


# ────────────────────────────────
# Main programme
# ────────────────────────────────
if PROTOCOL == 'http':
   url = "http://localhost:9000/mcp"
else:
   url = "http://localhost:9000/sse"




async def main() -> None:
    client = Client(
        url,      # adjust to your endpoint
        progress_handler=cli_progress,
        log_handler=cli_log,              # <- NEW
    )

    async with client:
        # long_task is expected to call ctx.report_progress() and ctx.info()
        result = await client.call_tool("long_task")

    print("✅ finished:", result[0].text)


if __name__ == "__main__":
    asyncio.run(main())
