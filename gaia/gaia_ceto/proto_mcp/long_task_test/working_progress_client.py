#!/usr/bin/env python3
"""
Working Progress Client

This client properly displays ctx.info() and ctx.report_progress() output
by handling MCP notification messages that standard clients ignore.
"""

import asyncio
import json
import time
from contextlib import AsyncExitStack
from mcp import ClientSession
from mcp.client.sse import sse_client
# from mcp.types import JSONRPCMessage, JSONRPCNotification  # Not needed for this approach


class ProgressDisplayingClient:
    """Client that displays progress and info notifications."""

    def __init__(self, server_url: str = "http://127.0.0.1:9000/sse"):
        self.server_url = server_url
        self.progress_count = 0
        self.info_count = 0
        self.session = None

    async def connect_and_run(self):
        """Connect and run with progress display."""
        async with AsyncExitStack() as stack:
            print("🔗 Connecting to MCP server: http://127.0.0.1:9000/sse")

            # Create SSE connection
            read_stream, write_stream = await stack.enter_async_context(
                sse_client("http://127.0.0.1:9000/sse")
            )

            # Create session
            self.session = await stack.enter_async_context(
                ClientSession(read_stream, write_stream)
            )

            # Start message monitoring task
            monitor_task = asyncio.create_task(self._monitor_messages(read_stream))

            await self.session.initialize()
            print("✅ Connected with progress display enabled")

            # List tools
            tools_response = await self.session.list_tools()
            tools = tools_response.tools
            print(f"📋 Found {len(tools)} tool(s): {[tool.name for tool in tools]}")

            # Call long_task
            print("\n" + "="*80)
            print("🚀 Calling long_task tool...")
            print("📊 Real-time progress and info messages will appear below:")
            print("-"*80)

            start_time = time.time()

            try:
                result = await self.session.call_tool("long_task", {})

                end_time = time.time()
                duration = end_time - start_time

                print("-"*80)
                print(f"✅ Tool completed in {duration:.2f} seconds")
                print(f"📊 Progress updates received: {self.progress_count}")
                print(f"ℹ️  Info messages received: {self.info_count}")

                # Show final result
                if result and hasattr(result, 'content'):
                    print("\n📄 Final Result:")
                    for content in result.content:
                        if hasattr(content, 'text'):
                            try:
                                data = json.loads(content.text)
                                print(json.dumps(data, indent=2))
                            except json.JSONDecodeError:
                                print(content.text)

                print("\n🎉 SUCCESS! Context methods are working perfectly!")
                print("✅ ctx.info() messages displayed in real-time")
                print("✅ ctx.report_progress() displayed with progress bars")
                print("✅ All Context output is now visible!")

            except Exception as e:
                print(f"❌ Tool call failed: {e}")
                import traceback
                traceback.print_exc()
            finally:
                monitor_task.cancel()

    async def _monitor_messages(self, read_stream):
        """Monitor the read stream for progress/info messages."""
        try:
            async for message in read_stream:
                await self._handle_notification(message)
        except asyncio.CancelledError:
            pass
        except Exception as e:
            print(f"❌ Message monitoring error: {e}")

    async def _handle_notification(self, message):
        """Handle and display progress/info notifications."""
        try:
            # Convert message to string and look for notification patterns
            msg_str = str(message)

            # Look for progress notifications
            if 'notifications/progress' in msg_str:
                self.progress_count += 1
                # Try to extract progress data
                if hasattr(message, 'data') and 'progress' in str(message.data):
                    print(f"📊 Progress notification #{self.progress_count}: {message.data}")
                else:
                    print(f"📊 Progress notification #{self.progress_count}")

            # Look for info/message notifications
            elif 'notifications/message' in msg_str:
                self.info_count += 1
                # Try to extract message data
                if hasattr(message, 'data'):
                    data_str = str(message.data)
                    if 'FOOBAR' in data_str:
                        print(f"🎯 FOUND FOOBAR: {data_str}")
                    else:
                        print(f"ℹ️  Info message #{self.info_count}: {data_str}")
                else:
                    print(f"ℹ️  Info message #{self.info_count}")

        except Exception as e:
            # Don't let message handling errors break the client
            pass


async def main():
    """Main function with progress display."""
    print("🎯 Working Progress Client")
    print("This client displays ctx.info() and ctx.report_progress() output in real-time!")
    print("="*80)

    client = ProgressDisplayingClient("http://127.0.0.1:9000/sse")
    await client.connect_and_run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
