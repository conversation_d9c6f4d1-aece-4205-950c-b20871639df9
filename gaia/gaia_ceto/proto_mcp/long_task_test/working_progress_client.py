#!/usr/bin/env python3
"""
Working Progress Client

This client properly displays ctx.info() and ctx.report_progress() output
by handling MCP notification messages that standard clients ignore.
"""

import asyncio
import json
import time
from contextlib import AsyncExitStack
from mcp import ClientSession
from mcp.client.sse import sse_client
from mcp.types import JSONRPCMessage, JSONRPCNotification


class ProgressDisplayingSession(ClientSession):
    """Client session that displays progress and info notifications."""
    
    def __init__(self, read_stream, write_stream):
        super().__init__(read_stream, write_stream)
        self.progress_count = 0
        self.info_count = 0
    
    async def _handle_message(self, message: JSONRPCMessage):
        """Override to handle and display progress/info notifications."""
        
        # Check if this is a notification we want to display
        if hasattr(message, 'root') and isinstance(message.root, JSONRPCNotification):
            notification = message.root
            
            # Handle progress notifications
            if notification.method == 'notifications/progress':
                self.progress_count += 1
                params = notification.params or {}
                progress = params.get('progress', 0)
                total = params.get('total', 1)
                token = params.get('progressToken', 'unknown')
                percentage = (progress / total * 100) if total > 0 else 0
                
                # Create progress bar
                bar_length = 30
                filled = int(bar_length * percentage / 100)
                bar = "█" * filled + "░" * (bar_length - filled)
                
                print(f"📊 [{bar}] {percentage:5.1f}% - Progress {progress}/{total} (token: {token})")
            
            # Handle info/log messages
            elif notification.method == 'notifications/message':
                self.info_count += 1
                params = notification.params or {}
                level = params.get('level', 'info')
                data = params.get('data', '')
                logger = params.get('logger', '')
                
                # Choose emoji based on level
                emoji = {
                    'info': 'ℹ️ ',
                    'warning': '⚠️ ',
                    'error': '❌',
                    'debug': '🔍'
                }.get(level, 'ℹ️ ')
                
                logger_text = f" [{logger}]" if logger else ""
                print(f"{emoji} {data}{logger_text}")
        
        # Always call parent handler for normal processing
        return await super()._handle_message(message)


async def main():
    """Main function with progress display."""
    print("🎯 Working Progress Client")
    print("This client displays ctx.info() and ctx.report_progress() output in real-time!")
    print("="*80)
    
    async with AsyncExitStack() as stack:
        print("🔗 Connecting to MCP server: http://127.0.0.1:9000/sse")
        
        # Create SSE connection
        read_stream, write_stream = await stack.enter_async_context(
            sse_client("http://127.0.0.1:9000/sse")
        )
        
        # Create progress-displaying session
        session = await stack.enter_async_context(
            ProgressDisplayingSession(read_stream, write_stream)
        )
        
        await session.initialize()
        print("✅ Connected with progress display enabled")
        
        # List tools
        tools_response = await session.list_tools()
        tools = tools_response.tools
        print(f"📋 Found {len(tools)} tool(s): {[tool.name for tool in tools]}")
        
        # Call long_task
        print("\n" + "="*80)
        print("🚀 Calling long_task tool...")
        print("📊 Real-time progress and info messages will appear below:")
        print("-"*80)
        
        start_time = time.time()
        
        try:
            result = await session.call_tool("long_task", {})
            
            end_time = time.time()
            duration = end_time - start_time
            
            print("-"*80)
            print(f"✅ Tool completed in {duration:.2f} seconds")
            print(f"📊 Progress updates received: {session.progress_count}")
            print(f"ℹ️  Info messages received: {session.info_count}")
            
            # Show final result
            if result and hasattr(result, 'content'):
                print("\n📄 Final Result:")
                for content in result.content:
                    if hasattr(content, 'text'):
                        try:
                            data = json.loads(content.text)
                            print(json.dumps(data, indent=2))
                        except json.JSONDecodeError:
                            print(content.text)
            
            print("\n🎉 SUCCESS! Context methods are working perfectly!")
            print("✅ ctx.info() messages displayed in real-time")
            print("✅ ctx.report_progress() displayed with progress bars")
            print("✅ All Context output is now visible!")
            
        except Exception as e:
            print(f"❌ Tool call failed: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
