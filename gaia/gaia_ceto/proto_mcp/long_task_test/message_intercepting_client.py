#!/usr/bin/env python3
"""
Message Intercepting Client

This client intercepts ALL MCP messages to find and display
ctx.info() and ctx.report_progress() output.
"""

import asyncio
import json
import time
from contextlib import AsyncExitStack
from mcp import ClientSession
from mcp.client.sse import sse_client
from mcp.types import JSONRPCMessage
import logging

# Set up logging to capture everything
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MessageInterceptor:
    """Intercepts and logs all MCP messages."""
    
    def __init__(self):
        self.message_count = 0
        self.progress_messages = []
        self.log_messages = []
        self.all_messages = []
    
    def intercept_message(self, direction: str, message: JSONRPCMessage):
        """Intercept and analyze a message.
        
        Args:
            direction: 'INCOMING' or 'OUTGOING'
            message: The MCP message
        """
        self.message_count += 1
        
        try:
            # Convert message to dict for analysis
            if hasattr(message, 'model_dump'):
                msg_dict = message.model_dump()
            elif hasattr(message, 'dict'):
                msg_dict = message.dict()
            else:
                msg_dict = str(message)
            
            # Store all messages
            self.all_messages.append({
                'direction': direction,
                'count': self.message_count,
                'message': msg_dict,
                'timestamp': time.time()
            })
            
            # Convert to string for searching
            msg_str = json.dumps(msg_dict, default=str)
            
            # Look for our FOOBAR markers
            if 'FOOBAR' in msg_str:
                print(f"🎯 FOUND FOOBAR MESSAGE #{self.message_count} ({direction}):")
                print(f"   {msg_str}")
                print()
            
            # Look for progress-related content
            if any(keyword in msg_str.lower() for keyword in ['progress', 'report_progress']):
                self.progress_messages.append(msg_dict)
                print(f"📊 PROGRESS MESSAGE #{self.message_count} ({direction}):")
                print(f"   {msg_str}")
                print()
            
            # Look for log/info content
            if any(keyword in msg_str.lower() for keyword in ['log', 'info', 'notification']):
                self.log_messages.append(msg_dict)
                print(f"ℹ️  LOG/INFO MESSAGE #{self.message_count} ({direction}):")
                print(f"   {msg_str}")
                print()
            
            # Show all messages for debugging
            print(f"🔍 MESSAGE #{self.message_count} ({direction}): {msg_str[:200]}...")
            
        except Exception as e:
            print(f"❌ Error intercepting message: {e}")
    
    def print_summary(self):
        """Print a summary of intercepted messages."""
        print("\n" + "="*80)
        print("📊 MESSAGE INTERCEPTION SUMMARY")
        print("="*80)
        print(f"Total messages: {self.message_count}")
        print(f"Progress messages: {len(self.progress_messages)}")
        print(f"Log/Info messages: {len(self.log_messages)}")
        
        if self.progress_messages:
            print("\n📊 PROGRESS MESSAGES FOUND:")
            for i, msg in enumerate(self.progress_messages, 1):
                print(f"  {i}. {json.dumps(msg, indent=2)}")
        
        if self.log_messages:
            print("\nℹ️  LOG/INFO MESSAGES FOUND:")
            for i, msg in enumerate(self.log_messages, 1):
                print(f"  {i}. {json.dumps(msg, indent=2)}")
        
        print("="*80)


class InterceptingClientSession(ClientSession):
    """Client session that intercepts all messages."""
    
    def __init__(self, read_stream, write_stream, interceptor: MessageInterceptor):
        self.interceptor = interceptor
        super().__init__(read_stream, write_stream)
    
    async def _send_message(self, message: JSONRPCMessage):
        """Override to intercept outgoing messages."""
        self.interceptor.intercept_message("OUTGOING", message)
        return await super()._send_message(message)
    
    async def _handle_message(self, message: JSONRPCMessage):
        """Override to intercept incoming messages."""
        self.interceptor.intercept_message("INCOMING", message)
        return await super()._handle_message(message)


async def main():
    """Main function with message interception."""
    print("🕵️ Message Intercepting Client")
    print("This client will capture and display ALL MCP messages")
    print("Looking specifically for ctx.info() and ctx.report_progress() output")
    print("="*80)
    
    interceptor = MessageInterceptor()
    
    async with AsyncExitStack() as stack:
        print("🔗 Connecting to MCP server: http://127.0.0.1:9000/sse")
        
        # Create SSE connection
        read_stream, write_stream = await stack.enter_async_context(
            sse_client("http://127.0.0.1:9000/sse")
        )
        
        # Create intercepting session
        session = await stack.enter_async_context(
            InterceptingClientSession(read_stream, write_stream, interceptor)
        )
        
        await session.initialize()
        print("✅ Connected with message interception enabled")
        
        # List tools
        print("\n🔍 Listing tools...")
        tools_response = await session.list_tools()
        tools = tools_response.tools
        print(f"📋 Found {len(tools)} tool(s): {[tool.name for tool in tools]}")
        
        # Call long_task
        print("\n" + "="*80)
        print("🚀 Calling long_task tool...")
        print("🕵️ ALL MCP messages will be intercepted and displayed below:")
        print("-"*80)
        
        start_time = time.time()
        
        try:
            result = await session.call_tool("long_task", {})
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n✅ Tool completed in {duration:.2f} seconds")
            
            # Show final result
            if result and hasattr(result, 'content'):
                print("\n📄 Final Result:")
                for content in result.content:
                    if hasattr(content, 'text'):
                        try:
                            data = json.loads(content.text)
                            print(json.dumps(data, indent=2))
                        except json.JSONDecodeError:
                            print(content.text)
            
            # Print interception summary
            interceptor.print_summary()
            
        except Exception as e:
            print(f"❌ Tool call failed: {e}")
            import traceback
            traceback.print_exc()
            
            # Still print summary even if tool failed
            interceptor.print_summary()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
