#!/usr/bin/env python3
"""
Simple Debug Client

This client just enables debug logging to show the MCP messages
that we know are being sent (from the message_intercepting_client.py output).
"""

import asyncio
import json
import time
import logging
from contextlib import AsyncExitStack
from mcp import ClientSession
from mcp.client.sse import sse_client

# Enable debug logging to see all MCP messages
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Filter to only show the messages we care about
class FOOBARFilter(logging.Filter):
    def filter(self, record):
        # Only show messages that contain FOOBAR or are progress/notification related
        msg = str(record.getMessage())
        return any(keyword in msg for keyword in [
            'FOOBAR', 
            'notifications/message', 
            'notifications/progress',
            'Received server message',
            'Progress',
            'Info'
        ])

# Apply filter to the SSE client logger
sse_logger = logging.getLogger('mcp.client.sse')
sse_logger.addFilter(FOOBARFilter())


async def main():
    """Main function with debug logging."""
    print("🔍 Simple Debug Client")
    print("This client uses debug logging to show ctx.info() and ctx.report_progress() messages")
    print("Look for 'FOOBAR' strings in the debug output below!")
    print("="*80)
    
    async with AsyncExitStack() as stack:
        print("🔗 Connecting to MCP server: http://127.0.0.1:9000/sse")
        
        # Create SSE connection
        read_stream, write_stream = await stack.enter_async_context(
            sse_client("http://127.0.0.1:9000/sse")
        )
        
        # Create session
        session = await stack.enter_async_context(
            ClientSession(read_stream, write_stream)
        )
        
        await session.initialize()
        print("✅ Connected with debug logging enabled")
        
        # List tools
        tools_response = await session.list_tools()
        tools = tools_response.tools
        print(f"📋 Found {len(tools)} tool(s): {[tool.name for tool in tools]}")
        
        # Call long_task
        print("\n" + "="*80)
        print("🚀 Calling long_task tool...")
        print("👀 Watch the debug logs below for FOOBAR messages!")
        print("-"*80)
        
        start_time = time.time()
        
        try:
            result = await session.call_tool("long_task", {})
            
            end_time = time.time()
            duration = end_time - start_time
            
            print("-"*80)
            print(f"✅ Tool completed in {duration:.2f} seconds")
            
            # Show final result
            if result and hasattr(result, 'content'):
                print("\n📄 Final Result:")
                for content in result.content:
                    if hasattr(content, 'text'):
                        try:
                            data = json.loads(content.text)
                            print(json.dumps(data, indent=2))
                        except json.JSONDecodeError:
                            print(content.text)
            
            print("\n💡 Summary:")
            print("✅ If you saw 'FOOBAR' strings in the debug logs above, Context methods are working!")
            print("✅ The debug logs show the actual MCP protocol messages being sent")
            print("✅ Standard MCP clients just don't display these messages by default")
            
        except Exception as e:
            print(f"❌ Tool call failed: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
