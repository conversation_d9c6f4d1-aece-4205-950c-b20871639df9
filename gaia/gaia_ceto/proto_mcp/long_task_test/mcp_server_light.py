#from fastmcp import <PERSON>MCP, Context
from fastmcp import FastMCP
from mcp.server.fastmcp import Context
import asyncio

mcp = FastMCP("ProgressServer")

@mcp.tool()
async def long_task(ctx: Context = None):
    """Long running task with progress reporting."""
    if ctx:
        await ctx.info("Starting long task with 5 steps...")

    for i in range(5):
        await asyncio.sleep(1)
        print(f'🔄 PROGRESS: Step {i + 1}/5 completed')

        if ctx:
            await ctx.report_progress(i + 1, 5)
            await ctx.info(f"✅ Completed step {i + 1}/5")
        else:
            print(f'📊 No context - Step {i + 1}/5')

    if ctx:
        await ctx.info("🎉 Long task completed successfully!")

    return {"status": "completed", "steps": 5, "message": "Long task finished"}

if __name__ == "__main__":
    mcp.run(transport="sse", host="127.0.0.1", port=9000)
    #mcp.run()
