#from fastmcp import <PERSON>MC<PERSON>, Context
from mcp.server.fastmcp import Context
from mcp.server.fastmcp import Fast<PERSON>P
import asyncio

mcp = FastMCP("ProgressServer")

@mcp.tool()
async def long_task(ctx: Context):
    for i in range(5):
        await asyncio.sleep(1)
        print('PROGRESS ', i + 1)
        await ctx.report_progress(i + 1, 5)
        await ctx.info(f"Completed step {i + 1}")
    return {"status": "done"}

if __name__ == "__main__":
    #mcp.run(transport="sse", host="127.0.0.1", port=9000)
    mcp.run()
