#from fastmcp import <PERSON><PERSON><PERSON>, Context
from mcp.server.fastmcp import FastMCP, Context
import asyncio

mcp = FastMCP("ProgressServer")

@mcp.tool()
async def long_task(ctx: Context = None):
    """Long running task with progress reporting."""
    print(f"🔍 DEBUG: long_task called with ctx = {ctx}, type = {type(ctx)}")

    await ctx.info(f"/////////////////////////////// Context...")

    if ctx:
        print("✅ Context injection working!")
        print(f"🔍 DEBUG: Context object: {ctx}")
        print(f"🔍 DEBUG: Context methods: {[m for m in dir(ctx) if not m.startswith('_')]}")

        print("🔍 DEBUG: About to call ctx.info() with 'CONTEXT FOOBAR INITIAL'")
        print(f"🔍 DEBUG: Context session: {getattr(ctx, 'session', 'No session')}")
        print(f"🔍 DEBUG: Context request_id: {getattr(ctx, 'request_id', 'No request_id')}")
        print(f"🔍 DEBUG: Context client_id: {getattr(ctx, 'client_id', 'No client_id')}")

        try:
            result = await ctx.info("CONTEXT FOOBAR INITIAL - Starting long task with 5 steps...")
            print(f"🔍 DEBUG: ctx.info() returned: {result}")
            print("🔍 DEBUG: ctx.info() completed successfully")

            # Try to inspect the session for any messages
            if hasattr(ctx, 'session'):
                print(f"🔍 DEBUG: Session type: {type(ctx.session)}")
                print(f"🔍 DEBUG: Session attributes: {[attr for attr in dir(ctx.session) if not attr.startswith('_')]}")

        except Exception as e:
            print(f"🔍 DEBUG: ctx.info() failed: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ No context injected")

    for i in range(5):
        await asyncio.sleep(1)
        print(f'🔄 PROGRESS: Step {i + 1}/5 completed')

        if ctx:
            print(f"🔍 DEBUG: About to call ctx.report_progress({i + 1}, 5, 'CONTEXT FOOBAR')")
            try:
                result = await ctx.report_progress(i + 1, 5, 'CONTEXT FOOBAR')
                print(f"🔍 DEBUG: ctx.report_progress() returned: {result}")
                print(f"🔍 DEBUG: ctx.report_progress() completed successfully")
            except Exception as e:
                print(f"🔍 DEBUG: ctx.report_progress() failed: {e}")
                import traceback
                traceback.print_exc()

            print(f"🔍 DEBUG: About to call ctx.info() for step {i + 1}")
            try:
                await ctx.info(f"CONTEXT FOOBAR STEP {i + 1} - ✅ Completed step {i + 1}/5")
                print(f"🔍 DEBUG: ctx.info() completed successfully")
            except Exception as e:
                print(f"🔍 DEBUG: ctx.info() failed: {e}")

            # Also try ctx.log() method
            print(f"🔍 DEBUG: About to call ctx.log() for step {i + 1}")
            try:
                await ctx.log(f"CONTEXT FOOBAR LOG {i + 1} - Log message for step {i + 1}/5")
                print(f"🔍 DEBUG: ctx.log() completed successfully")
            except Exception as e:
                print(f"🔍 DEBUG: ctx.log() failed: {e}")
        else:
            print(f'📊 No context - Step {i + 1}/5')

    if ctx:
        await ctx.info("🎉 Long task completed successfully!")

    return {"status": "completed", "steps": 5, "message": "Long task finished"}

if __name__ == "__main__":
    # Use uvicorn directly for more control
    import uvicorn

    # Create the SSE app
    app = mcp.sse_app()

    # Run with uvicorn
    uvicorn.run(app, host="127.0.0.1", port=9000, log_level="info")
