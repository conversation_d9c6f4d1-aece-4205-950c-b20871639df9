#from fastmcp import <PERSON><PERSON>P, Context
from mcp.server.fastmcp import FastMCP, Context
import asyncio

mcp = FastMCP("ProgressServer")

@mcp.tool()
async def long_task(ctx: Context = None):
    """Long running task with progress reporting."""
    print(f"🔍 DEBUG: long_task called with ctx = {ctx}, type = {type(ctx)}")

    await ctx.info(f"/////////////////////////////// Context...")

    if ctx:
        print("✅ Context injection working!")
        print("🔍 DEBUG: About to call ctx.info()")
        try:
            await ctx.info("Starting long task with 5 steps...")
            print("🔍 DEBUG: ctx.info() completed successfully")
        except Exception as e:
            print(f"🔍 DEBUG: ctx.info() failed: {e}")
    else:
        print("❌ No context injected")

    for i in range(5):
        await asyncio.sleep(1)
        print(f'🔄 PROGRESS: Step {i + 1}/5 completed')

        if ctx:
            print(f"🔍 DEBUG: About to call ctx.report_progress({i + 1}, 5, 'foo')")
            try:
                await ctx.report_progress(i + 1, 5, 'CONTEXT FOOBAR')
                print(f"🔍 DEBUG: ctx.report_progress() completed successfully")
            except Exception as e:
                print(f"🔍 DEBUG: ctx.report_progress() failed: {e}")

            print(f"🔍 DEBUG: About to call ctx.info() for step {i + 1}")
            try:
                await ctx.info(f"✅ Completed step {i + 1}/5")
                print(f"🔍 DEBUG: ctx.info() completed successfully")
            except Exception as e:
                print(f"🔍 DEBUG: ctx.info() failed: {e}")
        else:
            print(f'📊 No context - Step {i + 1}/5')

    if ctx:
        await ctx.info("🎉 Long task completed successfully!")

    return {"status": "completed", "steps": 5, "message": "Long task finished"}

if __name__ == "__main__":
    # Use uvicorn directly for more control
    import uvicorn

    # Create the SSE app
    app = mcp.sse_app()

    # Run with uvicorn
    uvicorn.run(app, host="127.0.0.1", port=9000, log_level="info")
