#!/usr/bin/env python3
"""
Minimal Progress Client

A minimal client that just calls long_task and shows verbose output.
Now displays ctx.info() and ctx.report_progress() messages in real-time!
"""

import asyncio
import json
import time
import logging
from contextlib import AsyncExitStack
from mcp import ClientSession
from mcp.client.sse import sse_client


class ContextMessageHandler:
    """Handler for displaying context messages in real-time."""

    def __init__(self):
        self.progress_count = 0
        self.info_count = 0
        self.messages = []

        # Set up logging to capture MCP messages
        self.setup_logging()

    def setup_logging(self):
        """Set up logging to capture and display context messages."""
        # Create a custom handler for MCP messages
        class ContextMessageFilter(logging.Filter):
            def __init__(self, handler):
                super().__init__()
                self.handler = handler

            def filter(self, record):
                msg = str(record.getMessage())

                # Look for server messages with notifications
                if 'Received server message' in msg and 'notifications/' in msg:
                    self.handler.handle_mcp_message(msg)

                # Don't actually log these (return False)
                return False

        # Set up the SSE client logger
        sse_logger = logging.getLogger('mcp.client.sse')
        sse_logger.setLevel(logging.DEBUG)
        sse_logger.addFilter(ContextMessageFilter(self))

        # Ensure we have a handler to capture the messages
        if not sse_logger.handlers:
            handler = logging.StreamHandler()
            handler.setLevel(logging.DEBUG)
            sse_logger.addHandler(handler)

    def handle_mcp_message(self, log_message):
        """Parse and display MCP notification messages."""
        try:
            # Extract the message content
            if 'notifications/message' in log_message:
                # Look for the data field in the log message
                if "'data': '" in log_message:
                    start = log_message.find("'data': '") + 9
                    end = log_message.find("'", start)
                    if start > 8 and end > start:
                        data = log_message[start:end]
                        self.info_count += 1

                        # Display with appropriate emoji
                        if 'FOOBAR' in data:
                            print(f"🎯 {data}")
                        elif '✅' in data:
                            print(f"ℹ️  {data}")
                        elif '🎉' in data:
                            print(f"🎉 {data}")
                        else:
                            print(f"ℹ️  {data}")

            elif 'notifications/progress' in log_message:
                self.progress_count += 1
                # Try to extract progress data
                if "'progress':" in log_message and "'total':" in log_message:
                    # Simple regex-like extraction
                    try:
                        progress_start = log_message.find("'progress': ") + 12
                        progress_end = log_message.find(",", progress_start)
                        total_start = log_message.find("'total': ") + 9
                        total_end = log_message.find("}", total_start)

                        if all(x > 0 for x in [progress_start-12, progress_end, total_start-9, total_end]):
                            progress = float(log_message[progress_start:progress_end])
                            total = float(log_message[total_start:total_end])
                            percentage = (progress / total * 100) if total > 0 else 0

                            # Create progress bar
                            bar_length = 30
                            filled = int(bar_length * percentage / 100)
                            bar = "█" * filled + "░" * (bar_length - filled)

                            print(f"📊 [{bar}] {percentage:5.1f}% - Progress {int(progress)}/{int(total)}")
                    except:
                        print(f"📊 Progress update #{self.progress_count}")
                else:
                    print(f"📊 Progress update #{self.progress_count}")

        except Exception as e:
            # Don't let message parsing errors break the client
            pass

    def get_summary(self):
        """Get a summary of captured messages."""
        return {
            'progress_count': self.progress_count,
            'info_count': self.info_count
        }


async def main():
    """Main function - connects and calls long_task with context message display."""
    print("🚀 Minimal Progress Client")
    print("This client calls long_task and displays ctx.info() and ctx.report_progress() in real-time!")
    print("=" * 80)

    # Create context message handler
    message_handler = ContextMessageHandler()

    async with AsyncExitStack() as stack:
        print("🔗 Connecting to MCP server: http://127.0.0.1:9000/sse")

        # Create SSE connection
        read_stream, write_stream = await stack.enter_async_context(
            sse_client("http://127.0.0.1:9000/sse")
        )

        # Create session
        session = await stack.enter_async_context(
            ClientSession(read_stream, write_stream)
        )

        await session.initialize()
        print("✅ Connected with context message display enabled")

        # List tools (for verification)
        tools_response = await session.list_tools()
        tools = tools_response.tools
        print(f"📋 Found {len(tools)} tool(s): {[tool.name for tool in tools]}")

        # Call long_task with context message monitoring
        print("\n" + "=" * 80)
        print("🚀 Calling long_task tool...")
        print("📊 Real-time ctx.info() and ctx.report_progress() messages will appear below:")
        print("-" * 80)

        start_time = time.time()

        try:
            # Call the tool
            print("🔄 Tool execution started...")
            result = await session.call_tool("long_task", {})

            end_time = time.time()
            duration = end_time - start_time

            print("-" * 80)
            print(f"✅ Tool completed successfully in {duration:.2f} seconds")

            # Get message summary
            summary = message_handler.get_summary()
            print(f"📊 Progress messages received: {summary['progress_count']}")
            print(f"ℹ️  Info messages received: {summary['info_count']}")

            # Show final result
            print("\n📄 Final Result:")
            if result and hasattr(result, 'content'):
                for content in result.content:
                    if hasattr(content, 'text'):
                        try:
                            data = json.loads(content.text)
                            print(json.dumps(data, indent=2))
                        except json.JSONDecodeError:
                            print(content.text)

            # Success summary
            print("\n" + "=" * 80)
            print("🎉 SUCCESS! Context methods are working perfectly!")
            print("✅ ctx.info() messages displayed in real-time above")
            print("✅ ctx.report_progress() displayed with progress bars above")
            print("✅ All Context output is now visible in the client!")
            print("✅ FastMCP Context injection is working correctly")
            print("=" * 80)

        except Exception as e:
            print(f"\n❌ Tool call failed: {e}")
            import traceback
            traceback.print_exc()


# Removed show_progress_indicator - we now display real context messages instead!


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
