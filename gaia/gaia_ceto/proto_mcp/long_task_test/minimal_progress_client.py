"""Concise Progress Client

Displays ctx.info and ctx.report_progress messages emitted by a Fast‑MCP
server in real‑time with emoji formatting.
"""

import asyncio
import json
import logging
import re
import time
from contextlib import AsyncExitStack
from mcp import ClientSession
from mcp.client.sse import sse_client

# ---------------------------------------------------------------------------
# Configuration
# ---------------------------------------------------------------------------
INFO_ICONS = {
    "🎉": "🎉",
    "✅": "✅",
    "❌": "❌",
    "⚠️": "⚠️",
    "debug": "🔍",
    "start": "🚀",
    "initial": "🚀",
    "complete": "✅",
    "finish": "✅",
}

PROGRESS_BAR_LEN = 30
PROGRESS_RE = re.compile(r"'progress':\s*(\d+(?:\.\d+)?),.*'total':\s*(\d+(?:\.\d+)?)")
DATA_RE = re.compile(r"'data':\s*'([^']+)'")


# ---------------------------------------------------------------------------
# Log filter that intercepts SSE messages and prints them nicely.
# ---------------------------------------------------------------------------
class ContextPrinter(logging.Filter):
    """Intercepts low‑level SSE log lines and pretty‑prints context messages."""

    def __init__(self) -> None:
        super().__init__()
        self.info_cnt = 0
        self.progress_cnt = 0

    # ---------------------------------------------------------------------
    # Private helpers
    # ---------------------------------------------------------------------
    def _print_info(self, text: str) -> None:
        self.info_cnt += 1
        icon = next((v for k, v in INFO_ICONS.items() if k.lower() in text.lower()), "ℹ️")
        print(f"{icon} {text}")

    def _print_progress(self, line: str) -> None:
        self.progress_cnt += 1
        if (m := PROGRESS_RE.search(line)) is None:
            print(f"📊 Progress update #{self.progress_cnt}")
            return

        progress, total = map(float, m.groups())
        pct = (progress / total * 100) if total else 0
        filled = int(PROGRESS_BAR_LEN * pct / 100)
        bar = "█" * filled + "░" * (PROGRESS_BAR_LEN - filled)
        print(f"📊 [{bar}] {pct:5.1f}%  {int(progress)}/{int(total)}")

    # ---------------------------------------------------------------------
    # logging.Filter interface
    # ---------------------------------------------------------------------
    def filter(self, record: logging.LogRecord) -> bool:  # noqa: D401
        msg = record.getMessage()
        if "Received server message" not in msg or "notifications/" not in msg:
            return False  # let other loggers handle it

        if "notifications/message" in msg:
            if (m := DATA_RE.search(msg)):
                self._print_info(m.group(1))
        else:
            self._print_progress(msg)

        return False  # swallow original log line


# ---------------------------------------------------------------------------
# Client entry‑point
# ---------------------------------------------------------------------------
async def main() -> None:  # noqa: D401
    print("🚀 Concise Progress Client")

    # Attach our filter to the internal SSE logger
    cp = ContextPrinter()
    sse_logger = logging.getLogger("mcp.client.sse")
    sse_logger.setLevel(logging.DEBUG)
    sse_logger.addFilter(cp)
    if not sse_logger.handlers:
        sse_logger.addHandler(logging.StreamHandler())

    async with AsyncExitStack() as stack:
        read, write = await stack.enter_async_context(sse_client("http://127.0.0.1:9000/sse"))
        session = await stack.enter_async_context(ClientSession(read, write))
        await session.initialize()
        print("✅ Connected to Fast‑MCP server")

        print("🚀 Executing long_task …")
        start = time.time()
        try:
            result = await session.call_tool("long_task", {})
            print(f"✅ Completed in {time.time() - start:.2f}s")
        except Exception as exc:
            print(f"❌ Tool failed: {exc}")
            return

        print(f"📊 Progress msgs: {cp.progress_cnt}   ℹ️ Info msgs: {cp.info_cnt}")

        # Pretty‑print returned content (if any)
        if getattr(result, "content", None):
            print("\n📄 Result:")
            for item in result.content:
                if hasattr(item, "text"):
                    try:
                        print(json.dumps(json.loads(item.text), indent=2))
                    except json.JSONDecodeError:
                        print(item.text)


if __name__ == "__main__":
    asyncio.run(main())
