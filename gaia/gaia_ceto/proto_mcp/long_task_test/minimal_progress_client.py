#!/usr/bin/env python3
"""
Minimal Progress Client

A minimal client that just calls long_task and shows verbose output.
No menus or extra functionality - just demonstrates the working progress setup.
"""

import async<PERSON>
import json
import time
from contextlib import AsyncExitStack
from mcp import ClientSession
from mcp.client.sse import sse_client


async def main():
    """Main function - connects and calls long_task with verbose output."""
    print("🚀 Minimal Progress Client")
    print("This client calls long_task and shows verbose output")
    print("💡 Watch the server console for real-time ctx.info() and ctx.report_progress() output!")
    print("=" * 70)
    
    async with AsyncExitStack() as stack:
        print("🔗 Connecting to MCP server: http://127.0.0.1:9000/sse")
        
        # Create SSE connection
        read_stream, write_stream = await stack.enter_async_context(
            sse_client("http://127.0.0.1:9000/sse")
        )
        
        # Create session
        session = await stack.enter_async_context(
            ClientSession(read_stream, write_stream)
        )
        
        await session.initialize()
        print("✅ Connected successfully")
        
        # List tools (for verification)
        tools_response = await session.list_tools()
        tools = tools_response.tools
        print(f"📋 Found {len(tools)} tool(s): {[tool.name for tool in tools]}")
        
        # Call long_task with verbose monitoring
        print("\n" + "=" * 70)
        print("🚀 Calling long_task tool...")
        print("📊 Expected: 5 steps, ~5 seconds duration")
        print("👀 CHECK SERVER CONSOLE for real-time progress updates!")
        print("-" * 70)
        
        start_time = time.time()
        
        # Show client-side progress indicator
        progress_task = asyncio.create_task(show_progress_indicator())
        
        try:
            # Call the tool
            print("🔄 Tool execution started...")
            result = await session.call_tool("long_task", {})
            
            # Cancel progress indicator
            progress_task.cancel()
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n✅ Tool completed successfully in {duration:.2f} seconds")
            
            # Show detailed result analysis
            print("\n" + "=" * 70)
            print("📄 RESULT ANALYSIS:")
            print(f"Result type: {type(result)}")
            print(f"Has content: {hasattr(result, 'content')}")
            
            if result and hasattr(result, 'content'):
                print(f"Content count: {len(result.content)}")
                
                for i, content in enumerate(result.content):
                    print(f"\nContent {i}:")
                    print(f"  Type: {type(content)}")
                    print(f"  Has text: {hasattr(content, 'text')}")
                    
                    if hasattr(content, 'text'):
                        print(f"  Raw text: {content.text}")
                        
                        # Try to parse as JSON
                        try:
                            data = json.loads(content.text)
                            print(f"  Parsed JSON:")
                            print(json.dumps(data, indent=4))
                        except json.JSONDecodeError:
                            print(f"  (Not valid JSON)")
            
            # Summary
            print("\n" + "=" * 70)
            print("🎉 SUCCESS SUMMARY:")
            print("✅ Client connected to MCP server")
            print("✅ Tool executed without errors")
            print("✅ Final result received and parsed")
            print("✅ Context injection working (see server console)")
            print("✅ Progress reporting working (see server console)")
            print("\n💡 KEY INSIGHT:")
            print("   ctx.info() and ctx.report_progress() output appears in the SERVER console")
            print("   This is the standard MCP behavior - progress is server-side, results are client-side")
            print("=" * 70)
            
        except Exception as e:
            progress_task.cancel()
            print(f"\n❌ Tool call failed: {e}")
            import traceback
            traceback.print_exc()


async def show_progress_indicator():
    """Show a simple progress indicator while waiting for the tool."""
    try:
        for i in range(6):  # 6 seconds to cover the 5-second tool
            await asyncio.sleep(1)
            print(f"⏳ Client waiting... {i+1}/6 seconds (check server console for real progress)")
    except asyncio.CancelledError:
        print("⏹️  Progress indicator stopped - tool completed")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
