#!/usr/bin/env python3
"""
Verification script to check timeout settings across the codebase

This script scans the codebase to verify that timeout settings have been
updated to support the echostring_longrunning tool.
"""

import os
import re
from pathlib import Path


def scan_file_for_timeouts(file_path):
    """Scan a file for timeout settings and return findings"""
    findings = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')

            for i, line in enumerate(lines, 1):
                # Look for tool_timeout patterns
                if 'tool_timeout' in line:
                    # Extract the timeout value - handle different patterns
                    timeout_match = re.search(r'tool_timeout[=:]\s*(\d+)', line)
                    kwargs_match = re.search(r'tool_timeout["\']?,\s*(\d+)', line)

                    timeout_value = None
                    if timeout_match:
                        timeout_value = int(timeout_match.group(1))
                    elif kwargs_match:
                        timeout_value = int(kwargs_match.group(1))

                    if timeout_value:
                        findings.append({
                            'line': i,
                            'content': line.strip(),
                            'timeout': timeout_value,
                            'sufficient': timeout_value >= 70
                        })

    except Exception as e:
        findings.append({
            'line': 0,
            'content': f"Error reading file: {e}",
            'timeout': 0,
            'sufficient': False
        })

    return findings


def main():
    """Main verification function"""
    print("🔍 MCP Timeout Settings Verification")
    print("=" * 50)
    print("Scanning codebase for tool_timeout settings...")
    print("Target: 70+ seconds for echostring_longrunning support")
    print("=" * 50)

    # Define files to check
    files_to_check = [
        "gaia/gaia_ceto/ceto_v002/chat_term.py",
        "gaia/djangaia/gaia_chat/api.py",
        "gaia/gaia_ceto/proto_mcp_http/mcp_http_streamlit.py",
        "gaia/gaia_ceto/proto_mcp_http/mcp_http_client_light.py",
        "gaia/gaia_ceto/proto_mcp/mcp_sse_client_light.py",
        "gaia/gaia_ceto/proto_mcp_http/mcp_http_clientlib.py",
        "gaia/gaia_ceto/proto_mcp/mcp_sse_clientlib.py",
    ]

    # Get the project root (assuming we're in gaia/gaia_ceto/proto_mcp/)
    current_dir = Path(__file__).parent
    project_root = current_dir.parent.parent.parent

    all_good = True
    total_files_checked = 0
    total_timeouts_found = 0

    for file_path in files_to_check:
        full_path = project_root / file_path

        if not full_path.exists():
            print(f"\n❌ File not found: {file_path}")
            all_good = False
            continue

        total_files_checked += 1
        print(f"\n📁 Checking: {file_path}")

        findings = scan_file_for_timeouts(full_path)

        if not findings:
            print("   ℹ️  No tool_timeout settings found")
        else:
            for finding in findings:
                total_timeouts_found += 1
                status = "✅" if finding['sufficient'] else "❌"
                print(f"   {status} Line {finding['line']}: {finding['content']}")
                if finding['timeout'] > 0:
                    print(f"      Timeout: {finding['timeout']} seconds ({'OK' if finding['sufficient'] else 'TOO LOW'})")

                if not finding['sufficient'] and finding['timeout'] > 0:
                    all_good = False

    # Summary
    print("\n" + "=" * 50)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 50)
    print(f"Files checked: {total_files_checked}")
    print(f"Timeout settings found: {total_timeouts_found}")
    print(f"Overall status: {'✅ ALL GOOD' if all_good else '❌ ISSUES FOUND'}")

    if all_good:
        print("\n🎉 All timeout settings are sufficient for echostring_longrunning!")
        print("The tool should work without timeout errors.")
    else:
        print("\n⚠️  Some timeout settings are too low.")
        print("Update any settings showing 'TOO LOW' to at least 70 seconds.")

    print("\n💡 Usage Tips:")
    print("- Use 'echostring_longrunning' in chat_term.py or web UI")
    print("- The tool takes ~60 seconds to complete")
    print("- Monitor progress with timestamps in the output")
    print("=" * 50)


if __name__ == "__main__":
    main()
