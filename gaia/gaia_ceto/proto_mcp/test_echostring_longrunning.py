#!/usr/bin/env python3
"""
Test script for the echostring_longrunning tool

This script tests the new echostring_longrunning tool to ensure it properly
simulates a long-running task with multiple stages and progress updates.
"""

import asyncio
import time
from mcp_tools import echostring_longrunning


async def test_longrunning_basic():
    """Test the basic functionality of echostring_longrunning"""
    print("Testing echostring_longrunning tool...")
    print("=" * 50)
    
    test_phrase = "TestTask"
    print(f"Starting long-running task with phrase: '{test_phrase}'")
    print("This should take approximately 60 seconds...")
    print("=" * 50)
    
    start_time = time.time()
    
    try:
        result = await echostring_longrunning(test_phrase)
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 50)
        print("TASK COMPLETED!")
        print("=" * 50)
        print(f"Actual duration: {duration:.1f} seconds")
        print("\nFull output:")
        print("-" * 30)
        print(result)
        print("-" * 30)
        
        # Verify the output contains expected elements
        lines = result.split('\n')
        print(f"\nOutput analysis:")
        print(f"- Total lines: {len(lines)}")
        print(f"- Contains phrase '{test_phrase}': {'Yes' if test_phrase in result else 'No'}")
        print(f"- Contains timestamps: {'Yes' if '[' in result and ']' in result else 'No'}")
        print(f"- Contains stages: {'Yes' if 'Stage' in result else 'No'}")
        print(f"- Duration close to 60s: {'Yes' if 55 <= duration <= 65 else 'No'}")
        
        # Check for all 6 stages
        stages_found = []
        for i in range(1, 7):
            if f"Stage {i}/6" in result:
                stages_found.append(i)
        
        print(f"- Stages found: {stages_found} ({'All 6 stages' if len(stages_found) == 6 else 'Missing stages'})")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error during long-running task: {e}")
        return False


async def test_longrunning_with_progress():
    """Test the long-running tool with progress monitoring"""
    print("\n" + "=" * 60)
    print("Testing with progress monitoring...")
    print("=" * 60)
    
    test_phrase = "ProgressTest"
    print(f"Starting monitored task with phrase: '{test_phrase}'")
    
    # Create a task that we can monitor
    task = asyncio.create_task(echostring_longrunning(test_phrase))
    
    start_time = time.time()
    
    # Monitor progress every 10 seconds
    while not task.done():
        await asyncio.sleep(10)
        elapsed = time.time() - start_time
        print(f"[MONITOR] Task still running... Elapsed: {elapsed:.1f}s")
        
        if elapsed > 70:  # Safety timeout
            print("[MONITOR] Safety timeout reached, cancelling task")
            task.cancel()
            break
    
    try:
        if not task.cancelled():
            result = await task
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n[MONITOR] Task completed in {duration:.1f} seconds")
            
            # Show just the first and last few lines
            lines = result.split('\n')
            print(f"\nFirst 3 lines:")
            for line in lines[:3]:
                print(f"  {line}")
            
            print(f"\nLast 3 lines:")
            for line in lines[-3:]:
                print(f"  {line}")
                
            return True
        else:
            print("[MONITOR] Task was cancelled")
            return False
            
    except Exception as e:
        print(f"[MONITOR] Error: {e}")
        return False


async def test_multiple_phrases():
    """Test with different phrases (shorter test)"""
    print("\n" + "=" * 60)
    print("Testing multiple phrases (Note: This will take several minutes!)")
    print("=" * 60)
    
    # For this test, we'll just start the tasks and cancel them after a few seconds
    # to verify they start correctly without waiting the full 60 seconds each
    
    test_phrases = ["Quick", "Test123", "LongPhrase"]
    
    for phrase in test_phrases:
        print(f"\nTesting phrase: '{phrase}'")
        task = asyncio.create_task(echostring_longrunning(phrase))
        
        # Let it run for 5 seconds to see the initial output
        await asyncio.sleep(5)
        
        # Cancel the task
        task.cancel()
        
        try:
            await task
        except asyncio.CancelledError:
            print(f"  ✅ Task for '{phrase}' started successfully and was cancelled")
        except Exception as e:
            print(f"  ❌ Error with '{phrase}': {e}")


if __name__ == "__main__":
    print("MCP Tools - echostring_longrunning Test")
    print("=" * 50)
    print("WARNING: This test includes a full 60-second run!")
    print("Press Ctrl+C to cancel if needed.")
    print("=" * 50)
    
    try:
        # Run the basic test (full 60 seconds)
        asyncio.run(test_longrunning_basic())
        
        # Run the progress monitoring test (another 60 seconds)
        asyncio.run(test_longrunning_with_progress())
        
        # Run the multiple phrases test (quick)
        asyncio.run(test_multiple_phrases())
        
        print("\n" + "=" * 60)
        print("All tests completed!")
        
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user (Ctrl+C)")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
