#!/usr/bin/env python3
"""
Quick test script for echostring_process_items function
"""

import asyncio
import sys
import os

# Add the parent directory to the path so we can import mcp_tools
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gaia.gaia_ceto.proto_mcp.mcp_tools import echostring_process_items

async def test_process_items():
    """Test the echostring_process_items function"""
    print("Testing echostring_process_items function...")
    
    try:
        # Test with a small number of items to verify it works
        result = await echostring_process_items("test", 3, None)
        print("✅ Function executed successfully!")
        print("Result preview:")
        print(result[:500] + "..." if len(result) > 500 else result)
        return True
    except Exception as e:
        print(f"❌ Function failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_process_items())
    sys.exit(0 if success else 1)
