# MCP Server Refactoring

## Overview

This document describes the refactoring of the MCP server implementations to eliminate code duplication between the SSE and HTTP versions.

## Problem

Previously, both `mcp_sse_server.py` and `mcp_http_server.py` contained identical tool functions:

- `echostring()`
- `get_company_categories_matching()`
- `get_llm_completion()`
- `get_top_companies()`
- `get_company_listing()`

This duplication made maintenance difficult and increased the risk of inconsistencies between the two implementations.

## Solution

### Created Shared Module

Created `gaia/gaia_ceto/proto_mcp/mcp_tools.py` containing:

- All common tool functions
- Shared global variables (e.g., `org_frame_cols`)
- Proper documentation and type hints
- Cleaned up code formatting

### Refactored Server Files

Both server files now:

1. Import the shared functions from `mcp_tools.py`
2. Register the imported functions with their respective MCP servers
3. Focus only on server-specific configuration and startup logic

## File Structure

```
gaia/gaia_ceto/
├── proto_mcp/
│   ├── mcp_tools.py          # Shared tool functions
│   ├── mcp_sse_server.py     # SSE server implementation
│   └── ...
└── proto_mcp_http/
    ├── mcp_http_server.py    # HTTP server implementation
    └── ...
```

## Benefits

1. **DRY Principle**: Functions are defined once and reused
2. **Consistency**: Both servers use identical tool implementations
3. **Maintainability**: Changes only need to be made in one place
4. **Testability**: Shared functions can be tested independently
5. **Clarity**: Server files focus on server-specific concerns

## Usage

Both servers work exactly as before:

```bash
# SSE Server (default port 8000)
python -m gaia.gaia_ceto.proto_mcp.mcp_sse_server --port 8000

# HTTP Server (default port 9000)
python -m gaia.gaia_ceto.proto_mcp_http.mcp_http_server --port 9000
```

## Available Tools

The shared module currently provides these tools:

1. **echostring**: Simple text echo tool
2. **echostring_table**: Returns table data in JSON format (formatted as HTML table in web UI)
3. **echostring_longrunning**: Simulates a 60-second long-running task with progress updates
4. **get_company_categories_matching**: Get company categories
5. **get_llm_completion**: Get companies sorted by criteria
6. **get_top_companies**: Get top companies by criteria
7. **get_company_listing**: Search companies by domain/name

## Table Data Format

The `echostring_table` tool returns JSON data in this format:

```json
{
    "type": "table",
    "title": "Table Title",
    "headers": ["Column 1", "Column 2", "Column 3"],
    "rows": [
        ["Row 1 Data", "More data", "Even more"],
        ["Row 2 Data", "More data", "Even more"]
    ]
}
```

The web UI automatically detects this format and renders it as an HTML table with:
- Styled headers with background color
- Alternating row colors
- Bordered cells
- Responsive design

## Long-Running Task Simulation

The `echostring_longrunning` tool simulates a realistic long-running task that takes approximately 60 seconds to complete. It's useful for testing:

- How the MCP system handles long-running operations
- Tool timeout behavior
- Progress monitoring and user experience
- Async operation handling

### Task Structure

The tool executes 6 stages, each taking ~10 seconds:

1. **Initialization**: Setting up environment
2. **Data Processing**: Analyzing input data
3. **Computation**: Running calculations
4. **Validation**: Quality assurance checks
5. **Finalization**: Preparing output
6. **Completion**: Final cleanup and results

### Output Format

The tool returns a detailed log with:
- Timestamps for each step (`[HH:MM:SS]`)
- Stage progress indicators (`Stage X/6`)
- Descriptive progress messages
- Final duration and results

### Example Output

```
[14:30:15] Starting long-running task with phrase: 'test'
[14:30:15] Stage 1/6: Initializing...
[14:30:20] - Setting up environment for 'test'
[14:30:25] - Initialization complete
[14:30:25] Stage 2/6: Processing data...
...
[14:31:15] Task completed successfully!
[14:31:15] Total duration: 60.1 seconds
[14:31:15] Final result: test, test, test - long-running task complete!
```

### Timeout Configuration

**Important**: The `echostring_longrunning` tool requires a timeout of at least 70 seconds. The following components have been updated with appropriate timeout settings:

- **chat_term.py**: 70-second timeout for MCP tool calls
- **Web API (gaia_chat)**: 70-second timeout for web interface
- **Streamlit apps**: 70-second timeout for Streamlit interfaces
- **Client libraries**: 70-second timeout in test and demo scripts

If you encounter timeout errors, ensure your MCP client is configured with a timeout of at least 70 seconds.

## Adding New Tools

To add new tools:

1. Add the function to `mcp_tools.py`
2. Import it in both server files
3. Register it with `mcp.tool()` in both servers

Example:

```python
# In mcp_tools.py
async def new_tool(param: str = Field(description="Description")) -> str:
    """New tool function"""
    return f"Result: {param}"

# In both server files
from gaia.gaia_ceto.proto_mcp.mcp_tools import new_tool
mcp.tool()(new_tool)
```
