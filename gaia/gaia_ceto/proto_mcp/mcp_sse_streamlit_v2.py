"""
MCP SSE Streamlit Client (Version 2)

This is a Streamlit-based client for interacting with MCP servers via SSE.
It uses the MCPClientLib for core functionality.
"""

import streamlit as st
import asyncio
import json
import time
import pandas as pd
from datetime import datetime
from typing import Optional, List, Dict, Any

# Import our client library
from mcp_sse_clientlib import MC<PERSON><PERSON><PERSON><PERSON>, ToolCallResult

# --- Configuration ---
DEFAULT_SERVER_URL = "http://0.0.0.0:8000/sse"  # Default URL for SSE
DEFAULT_MODEL = "claude-3-5-sonnet-20240620"  # Default Claude model

# Helper functions
def format_json(obj):
    """Format JSON for display in Streamlit."""
    if obj is None:
        return "None"
    try:
        if isinstance(obj, str):
            # Try to parse as JSON if it's a string
            try:
                parsed = json.loads(obj)
                return json.dumps(parsed, indent=2)
            except:
                return obj
        else:
            # Convert to JSON if it's an object
            return json.dumps(obj, indent=2, default=str)
    except:
        return str(obj)

def debug_tool_call(tool_result: ToolCallResult, container=None):
    """Debug helper to display detailed tool call information and store in session state."""
    # Tool metadata
    metadata = f"**Tool:** {tool_result.tool_name}\n"
    if tool_result.execution_time:
        metadata += f"**Execution Time:** {tool_result.execution_time:.2f} seconds\n"

    # Add simulated flag if applicable
    if hasattr(tool_result, 'simulated') and tool_result.simulated:
        metadata += f"**⚠️ SIMULATED RESULT ⚠️**\n"
        if hasattr(tool_result, 'error') and tool_result.error:
            metadata += f"**Original Error:** {tool_result.error}\n"

    # Input section
    metadata += f"\n**Input:**\n```json\n{format_json(tool_result.tool_input)}\n```"

    # Result section
    metadata += "\n\n**Result:**"

    # Format the result content
    is_tabular = False  # Initialize is_tabular to False by default

    if not tool_result.success:
        result_status = "❌ Tool execution failed"
        result_content = tool_result.error or "No error details"
    else:
        # Show simulated status in the result status
        if hasattr(tool_result, 'simulated') and tool_result.simulated:
            result_status = "⚠️ Simulated result (actual call failed)"
        else:
            result_status = "✅ Tool executed successfully"

        # Get content safely
        content = tool_result.content if hasattr(tool_result, 'content') else str(tool_result)

        # Check if it's tabular data
        if isinstance(content, str) and '\n' in content and '  ' in content:
            result_content = content
            is_tabular = True
        else:
            result_content = str(content)

    # Store in session state for persistent display
    debug_entry = {
        "title": f"🛠️ Tool Call: {tool_result.tool_name} ({result_status})",
        "content": metadata,
        "code": result_content,
        "language": "text",
        "timestamp": datetime.now().isoformat(),
        "is_tabular": is_tabular
    }

    if 'tool_debug_info' not in st.session_state:
        st.session_state.tool_debug_info = []

    # Add to the beginning of the list (most recent first)
    st.session_state.tool_debug_info.insert(0, debug_entry)

    # Limit the number of stored debug entries to prevent memory issues
    if len(st.session_state.tool_debug_info) > 20:
        st.session_state.tool_debug_info = st.session_state.tool_debug_info[:20]

    # Also display in the container if provided
    if container is not None:
        # Add simulated warning to the title if applicable
        if hasattr(tool_result, 'simulated') and tool_result.simulated:
            container.markdown(f"### 🛠️ Tool Call: {tool_result.tool_name} ⚠️ SIMULATED")
            # Show warning about simulated result
            container.warning("⚠️ This is a simulated result because the actual tool call failed.")
        else:
            container.markdown(f"### 🛠️ Tool Call: {tool_result.tool_name}")

        container.markdown(metadata)

        if not tool_result.success:
            container.error(result_status)
            container.code(result_content, language="text")
        else:
            # Use different status indicators based on whether it's simulated
            if hasattr(tool_result, 'simulated') and tool_result.simulated:
                container.warning(result_status)
                # Show the original error if available
                if hasattr(tool_result, 'error') and tool_result.error:
                    container.error(f"Original error: {tool_result.error}")
            else:
                container.success(result_status)

            if is_tabular:
                container.markdown("*Detected possible tabular data:*")
                container.text(result_content)

                # Try to convert to DataFrame if it looks like a table
                try:
                    if '   ' in result_content and '\n' in result_content:
                        # Try to parse as fixed-width format
                        from io import StringIO
                        df = pd.read_fwf(StringIO(result_content))
                        container.dataframe(df, use_container_width=True)
                except Exception as e:
                    container.warning(f"Could not parse as table: {str(e)}")
            elif len(result_content) > 500:
                container.markdown(f"*Long content ({len(result_content)} chars)*")
                container.code(result_content[:500] + "...", language="text")
                if container.button("Show Full Content", key=f"full_{tool_result.tool_name}_{datetime.now().timestamp()}"):
                    container.code(result_content, language="text")
            else:
                container.code(result_content, language="text")

class StreamlitMCPClient:
    """Streamlit wrapper for the MCPClientLib."""

    def __init__(self):
        """Initialize the Streamlit MCP client."""
        self.client = None
        self.connected = False
        self.messages = []
        self.tool_calls_history = []

    def debug_callback(self, level: str, message: str, data: Any = None):
        """Callback for debug messages from the client library."""
        # Store debug information in session state
        if 'debug_entries' not in st.session_state:
            st.session_state.debug_entries = []

        # Create a new debug entry
        debug_entry = {
            "timestamp": datetime.now().isoformat(),
            "title": f"🔍 {level.upper()}: {message[:50]}{'...' if len(message) > 50 else ''}",
            "content": f"""
**Level:** {level.upper()}
**Message:** {message}
**Time:** {datetime.now().strftime('%H:%M:%S')}
"""
        }

        # Add data if provided
        if data is not None:
            if isinstance(data, dict) or isinstance(data, list):
                debug_entry["content"] += f"\n**Data:**\n```json\n{format_json(data)}\n```"
            else:
                debug_entry["content"] += f"\n**Data:** {data}"

        # Add to the beginning of the list (most recent first)
        st.session_state.debug_entries.insert(0, debug_entry)

        # Limit the number of stored debug entries
        if len(st.session_state.debug_entries) > 50:
            st.session_state.debug_entries = st.session_state.debug_entries[:50]

        # Also show in sidebar for immediate feedback if it's an error or warning
        if level in ["error", "warning"]:
            if level == "error":
                st.sidebar.error(message)
            else:
                st.sidebar.warning(message)

        # If debug mode is enabled, also print to the console
        if hasattr(st.session_state, 'debug_mode') and st.session_state.debug_mode:
            print(f"[{level.upper()}] {message}")
            if data:
                print(f"  Data: {data}")

        # Return None to indicate this is not a coroutine
        return None

    async def connect_to_server(self, server_url: str, anthropic_api_key: Optional[str] = None) -> bool:
        """Connect to an MCP server via SSE transport."""
        st.info(f"Connecting to MCP server via SSE at: {server_url}")

        # Create the client with our debug callback and API key if provided
        self.client = MCPClientLib(
            anthropic_api_key=anthropic_api_key,
            debug_callback=self.debug_callback
        )

        # Connect to the server
        success = await self.client.connect_to_server(server_url)

        if success:
            st.success(f"Connected to server with tools: {', '.join([tool['name'] for tool in self.client.available_tools])}")
            self.connected = True
        else:
            st.error("Failed to connect to server.")
            self.connected = False

        return success

    async def process_query(self, query: str) -> str:
        """
        Process a query using Claude and available tools.
        Uses the direct approach (like client_light.py) - creating a fresh client for each call.
        """
        st.info("Processing query...")

        # Add timing information
        start_time = time.time()
        st.sidebar.markdown(f"### 🕒 Query Processing")
        st.sidebar.info(f"Starting query processing at {datetime.now().strftime('%H:%M:%S')}")

        # Create a fresh client for this specific query
        self.debug_callback("info", "Creating new MCPClientLib instance for this query")
        fresh_client = MCPClientLib(
            anthropic_api_key=self.client.anthropic.api_key if self.client and hasattr(self.client, 'anthropic') else None,
            debug_callback=self.debug_callback
        )

        # Connect to the server
        server_url = DEFAULT_SERVER_URL  # Use the default or get from session state
        self.debug_callback("info", f"Connecting to server at {server_url}")
        success = await fresh_client.connect_to_server(server_url)

        if not success:
            self.debug_callback("error", "Failed to connect to server for this query")
            return "Error: Failed to connect to server for this query."

        self.debug_callback("info", "Connected successfully for this query")

        # Process the query with the fresh client
        result = await fresh_client.process_query(
            query=query,
            model=DEFAULT_MODEL,
            max_tokens=1024,
            tool_timeout=30,  # 30 second timeout for tool calls
            conversation_history=self.messages
        )

        # Clean up the fresh client
        self.debug_callback("info", "Cleaning up fresh client")
        await fresh_client.cleanup()

        # Add timing information
        elapsed = time.time() - start_time
        st.sidebar.success(f"Query processing completed in {elapsed:.2f} seconds")

        # Update conversation history
        self.messages = result["messages"]

        # Add tool results to history
        for tool_result in result["tool_results"]:
            self.tool_calls_history.append(tool_result)
            # Display debug information
            debug_tool_call(tool_result, container=st.sidebar)

        # Return the final text
        return result["final_text"]

    async def cleanup(self):
        """Clean up resources."""
        st.info("Cleaning up resources...")
        if self.client:
            await self.client.cleanup()
        self.connected = False
        st.success("Cleanup complete.")

# Streamlit app
def main():
    st.set_page_config(
        page_title="MCP SSE Client",
        page_icon="🤖",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    st.title("MCP SSE Client (v2)")
    st.markdown("*Using the direct approach - creating a fresh client for each operation*")

    with st.expander("About This App", expanded=False):
        st.markdown("""
        ### How This App Works

        This app uses the direct approach (like client_light.py):

        1. **Fresh Client**: Creates a new client for each operation
        2. **Clean Connection**: Establishes a new connection for each call
        3. **Proper Cleanup**: Cleans up resources after each call

        This approach avoids the issues with trying to maintain a persistent connection
        across Streamlit reruns, making tool calls work reliably.
        """)

    # Check for Anthropic API key in environment
    import os
    if "ANTHROPIC_API_KEY" not in os.environ:
        st.warning("""
        ⚠️ No Anthropic API key found in environment variables.

        You will need to provide an API key in the connection panel to use Claude.

        You can also set the ANTHROPIC_API_KEY environment variable before running the app.
        """)

    # Initialize session state
    if 'client' not in st.session_state:
        st.session_state.client = None
        st.session_state.connected = False
        st.session_state.messages = []
        st.session_state.debug_entries = []  # Store debug logs
        st.session_state.tool_debug_info = []  # Store tool debug info
        st.session_state.debug_mode = False  # Enable/disable detailed debugging

    # Create a two-column layout
    col1, col2 = st.columns([3, 1])

    # Debug panel in the right column
    with col2:
        st.markdown("### Debug Info")

        # Add debug mode toggle
        st.session_state.debug_mode = st.checkbox("Enable Detailed Debug Mode", value=st.session_state.debug_mode)

        debug_tab1, debug_tab2 = st.tabs(["Tool Calls", "Debug History"])

        # Tool Calls Debug Tab
        with debug_tab1:
            if 'tool_debug_info' in st.session_state and st.session_state.tool_debug_info:
                for debug_entry in st.session_state.tool_debug_info:
                    with st.expander(f"{debug_entry['title']}", expanded=False):
                        st.markdown(debug_entry['content'])
                        if 'code' in debug_entry:
                            st.code(debug_entry['code'], language=debug_entry.get('language', 'text'))
            else:
                st.info("No tool calls recorded yet.")

        # Debug History Tab - This will persist across reruns
        with debug_tab2:
            st.markdown("### 📜 Debug History")
            st.markdown("This history persists across reruns and shows all debug information.")

            if 'debug_entries' in st.session_state and st.session_state.debug_entries:
                # Add a button to clear the history
                if st.button("Clear Debug History"):
                    st.session_state.debug_entries = []
                    st.success("Debug history cleared!")
                    st.rerun()

                # Display all debug entries
                for i, entry in enumerate(st.session_state.debug_entries):
                    with st.expander(f"{entry['title']} ({i+1}/{len(st.session_state.debug_entries)})", expanded=(i==0)):
                        st.markdown(f"**Time:** {entry['timestamp']}")
                        st.markdown(entry['content'])
            else:
                st.info("No debug history recorded yet.")

    # Main content in the left column
    with col1:
        # Server connection section
        with st.expander("Server Connection", expanded=not st.session_state.connected):
            server_url = st.text_input("Server URL", value=DEFAULT_SERVER_URL)

            # Add Anthropic API key input
            anthropic_api_key = st.text_input("Anthropic API Key", value="", type="password",
                                             help="Enter your Anthropic API key. Leave empty to use environment variable.")

            if st.button("Connect to Server"):
                # Create a new client and connect
                async def connect():
                    client = StreamlitMCPClient()
                    # Pass the API key if provided
                    api_key = anthropic_api_key if anthropic_api_key else None
                    success = await client.connect_to_server(server_url, anthropic_api_key=api_key)
                    if success:
                        st.session_state.client = client
                        st.session_state.connected = True
                        st.rerun()

                # Use a safer approach for running async code in Streamlit
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(connect())
                    loop.close()
                except Exception as e:
                    st.error(f"Connection error: {e}")
                    import traceback
                    st.code(traceback.format_exc(), language="python")

        # Main interface with tabs
        if st.session_state.connected and st.session_state.client:
            # Create tabs for chat and debug
            tab1, tab2 = st.tabs(["💬 Chat", "🔍 Tool Details"])

            # Chat tab
            with tab1:
                # Display chat history
                for message in st.session_state.messages:
                    if message["role"] == "user":
                        st.chat_message("user").write(message["content"])
                    else:
                        st.chat_message("assistant").write(message["content"])

                # Input for new messages
                if prompt := st.chat_input("Enter your query..."):
                    # Add user message to chat history
                    st.session_state.messages.append({"role": "user", "content": prompt})
                    st.chat_message("user").write(prompt)

                    # Process the query
                    async def process():
                        response = await st.session_state.client.process_query(prompt)
                        st.session_state.messages.append({"role": "assistant", "content": response})
                        st.rerun()

                    # Use a safer approach for running async code in Streamlit
                    try:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(process())
                        loop.close()
                    except Exception as e:
                        st.error(f"Processing error: {e}")
                        import traceback
                        st.code(traceback.format_exc(), language="python")

                # Disconnect button
                if st.button("Disconnect"):
                    async def disconnect():
                        if st.session_state.client:
                            await st.session_state.client.cleanup()
                        st.session_state.client = None
                        st.session_state.connected = False
                        st.rerun()

                    # Use a safer approach for running async code in Streamlit
                    try:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(disconnect())
                        loop.close()
                    except Exception as e:
                        st.error(f"Disconnect error: {e}")
                        import traceback
                        st.code(traceback.format_exc(), language="python")

            # Tool Details tab
            with tab2:
                st.header("MCP Tool Calls Debug")

                # Show available tools
                st.subheader("Available Tools")
                if hasattr(st.session_state.client.client, 'available_tools') and st.session_state.client.client.available_tools:
                    # Create a selectbox to choose which tool to view
                    tool_names = [tool['name'] for tool in st.session_state.client.client.available_tools]
                    selected_tool = st.selectbox("Select a tool to view details:", tool_names)

                    # Display the selected tool's details
                    for tool in st.session_state.client.client.available_tools:
                        if tool['name'] == selected_tool:
                            st.markdown(f"**Description:** {tool.get('description', 'No description')}")
                            st.markdown("**Input Schema:**")
                            st.code(format_json(tool.get('input_schema', {})), language="json")

                            # Add direct testing capability for tools
                            st.markdown("### 🧪 Test Tool Directly")

                            # Add a quick test for echostring
                            if tool['name'] == 'echostring':
                                st.markdown("#### Quick Test for Echostring")
                                quick_phrase = st.text_input("Enter a phrase to echo:", "Hello, world!")

                                col1, col2 = st.columns(2)
                                with col1:
                                    if st.button("Test with Dict Format"):
                                        async def quick_test_dict():
                                            start_time = time.time()
                                            st.info(f"Starting echostring call at {datetime.now().strftime('%H:%M:%S')}")

                                            # Create a fresh client for this specific tool call
                                            st.info("Creating fresh client for this tool call")
                                            fresh_client = MCPClientLib(
                                                anthropic_api_key=st.session_state.client.client.anthropic.api_key if hasattr(st.session_state.client.client, 'anthropic') else None,
                                                debug_callback=st.session_state.client.debug_callback
                                            )

                                            # Connect to the server
                                            st.info(f"Connecting to server at {DEFAULT_SERVER_URL}")
                                            success = await fresh_client.connect_to_server(DEFAULT_SERVER_URL)

                                            if not success:
                                                st.error("Failed to connect to server")
                                                return ToolCallResult(
                                                    tool_name="echostring",
                                                    tool_input={"phrase": quick_phrase},
                                                    tool_call_id="quick_test_dict",
                                                    success=False,
                                                    error="Failed to connect to server"
                                                )

                                            st.info("Connected successfully")

                                            # Call the tool
                                            result = await fresh_client.call_tool(
                                                tool_name="echostring",
                                                tool_input={"phrase": quick_phrase},
                                                tool_call_id="quick_test_dict",
                                                timeout=30
                                            )

                                            # Clean up
                                            st.info("Cleaning up client")
                                            await fresh_client.cleanup()

                                            elapsed = time.time() - start_time
                                            st.info(f"Call completed in {elapsed:.2f} seconds")
                                            return result

                                        try:
                                            loop = asyncio.new_event_loop()
                                            asyncio.set_event_loop(loop)
                                            result = loop.run_until_complete(quick_test_dict())
                                            loop.close()

                                            if result.success:
                                                st.success("Success!")
                                                st.code(result.content, language="text")
                                            else:
                                                st.error(f"Error: {result.error}")
                                        except Exception as e:
                                            st.error(f"Exception: {e}")

                                with col2:
                                    if st.button("Test with String Format"):
                                        async def quick_test_string():
                                            start_time = time.time()
                                            st.info(f"Starting echostring call at {datetime.now().strftime('%H:%M:%S')}")

                                            # Create a fresh client for this specific tool call
                                            st.info("Creating fresh client for this tool call")
                                            fresh_client = MCPClientLib(
                                                anthropic_api_key=st.session_state.client.client.anthropic.api_key if hasattr(st.session_state.client.client, 'anthropic') else None,
                                                debug_callback=st.session_state.client.debug_callback
                                            )

                                            # Connect to the server
                                            st.info(f"Connecting to server at {DEFAULT_SERVER_URL}")
                                            success = await fresh_client.connect_to_server(DEFAULT_SERVER_URL)

                                            if not success:
                                                st.error("Failed to connect to server")
                                                return ToolCallResult(
                                                    tool_name="echostring",
                                                    tool_input=quick_phrase,
                                                    tool_call_id="quick_test_string",
                                                    success=False,
                                                    error="Failed to connect to server"
                                                )

                                            st.info("Connected successfully")

                                            # Call the tool
                                            result = await fresh_client.call_tool(
                                                tool_name="echostring",
                                                tool_input=quick_phrase,
                                                tool_call_id="quick_test_string",
                                                timeout=30
                                            )

                                            # Clean up
                                            st.info("Cleaning up client")
                                            await fresh_client.cleanup()

                                            elapsed = time.time() - start_time
                                            st.info(f"Call completed in {elapsed:.2f} seconds")
                                            return result

                                        try:
                                            loop = asyncio.new_event_loop()
                                            asyncio.set_event_loop(loop)
                                            result = loop.run_until_complete(quick_test_string())
                                            loop.close()

                                            if result.success:
                                                st.success("Success!")
                                                st.code(result.content, language="text")
                                            else:
                                                st.error(f"Error: {result.error}")
                                        except Exception as e:
                                            st.error(f"Exception: {e}")

                            st.markdown("#### Advanced Testing")
                            test_input = st.text_area("Enter tool input (JSON format):", "{}")

                            if st.button(f"Test {selected_tool}"):
                                try:
                                    # Parse the input as JSON
                                    try:
                                        input_data = json.loads(test_input)
                                    except json.JSONDecodeError:
                                        st.error("Invalid JSON input. Please check your syntax.")
                                        input_data = None

                                    if input_data is not None:
                                        async def test_tool():
                                            # Create a fresh client for this specific tool call
                                            st.info("Creating fresh client for this tool call")
                                            fresh_client = MCPClientLib(
                                                anthropic_api_key=st.session_state.client.client.anthropic.api_key if hasattr(st.session_state.client.client, 'anthropic') else None,
                                                debug_callback=st.session_state.client.debug_callback
                                            )

                                            # Connect to the server
                                            st.info(f"Connecting to server at {DEFAULT_SERVER_URL}")
                                            success = await fresh_client.connect_to_server(DEFAULT_SERVER_URL)

                                            if not success:
                                                st.error("Failed to connect to server")
                                                return ToolCallResult(
                                                    tool_name=selected_tool,
                                                    tool_input=input_data,
                                                    tool_call_id="manual_test",
                                                    success=False,
                                                    error="Failed to connect to server"
                                                )

                                            st.info("Connected successfully")

                                            # Call the tool with a timeout
                                            result = await fresh_client.call_tool(
                                                tool_name=selected_tool,
                                                tool_input=input_data,
                                                tool_call_id="manual_test",
                                                timeout=30  # 30 second timeout
                                            )

                                            # Clean up
                                            st.info("Cleaning up client")
                                            await fresh_client.cleanup()
                                            return result

                                        with st.spinner(f"Calling {selected_tool}... (timeout: 30s)"):
                                            try:
                                                # Add timing information
                                                start_time = time.time()
                                                st.info(f"Starting tool call at {datetime.now().strftime('%H:%M:%S')}")

                                                loop = asyncio.new_event_loop()
                                                asyncio.set_event_loop(loop)
                                                result = loop.run_until_complete(test_tool())
                                                loop.close()

                                                elapsed = time.time() - start_time
                                                st.info(f"Tool call completed in {elapsed:.2f} seconds")

                                                # Display the result
                                                debug_tool_call(result)

                                                if result.success:
                                                    st.success(f"Tool executed successfully in {result.execution_time:.2f} seconds")
                                                    st.markdown("**Result:**")
                                                    st.code(result.content, language="text")
                                                else:
                                                    st.error(f"Tool execution failed: {result.error}")
                                            except Exception as e:
                                                st.error(f"Error testing tool: {e}")
                                                import traceback
                                                st.code(traceback.format_exc(), language="python")
                                except Exception as e:
                                    st.error(f"Error: {e}")
                            break
                else:
                    st.info("No tools available or not yet loaded.")

                # Show tool calls history
                st.subheader("Tool Calls History")
                if hasattr(st.session_state.client, 'tool_calls_history') and st.session_state.client.tool_calls_history:
                    # Create a DataFrame for better visualization
                    history_data = []
                    for call in st.session_state.client.tool_calls_history:
                        # Check if it's a simulated result
                        is_simulated = hasattr(call, 'simulated') and call.simulated

                        # Create status indicator
                        if is_simulated:
                            status = "⚠️ Simulated"
                        elif call.success:
                            status = "✅ Success"
                        else:
                            status = "❌ Failed"

                        history_data.append({
                            "Tool": call.tool_name,
                            "Status": status,
                            "Simulated": "Yes" if is_simulated else "No",
                            "Execution Time": f"{call.execution_time:.2f}s" if call.execution_time else "N/A",
                            "Input": str(call.tool_input)[:50] + "..." if len(str(call.tool_input)) > 50 else str(call.tool_input),
                        })

                    if history_data:
                        # Show the history as a dataframe
                        history_df = pd.DataFrame(history_data)
                        st.dataframe(history_df, use_container_width=True)

                        # Create a selectbox to choose which call to view in detail
                        call_options = []
                        for i, call in enumerate(st.session_state.client.tool_calls_history):
                            # Check if it's a simulated result
                            is_simulated = hasattr(call, 'simulated') and call.simulated

                            if is_simulated:
                                status = "⚠️ Simulated"
                            elif call.success:
                                status = "✅ Success"
                            else:
                                status = "❌ Failed"

                            call_options.append(f"Call {i+1}: {call.tool_name} - {status}")

                        selected_call = st.selectbox("Select a tool call to view details:", call_options)

                        # Get the index of the selected call
                        if selected_call:
                            selected_index = call_options.index(selected_call)
                            call = st.session_state.client.tool_calls_history[selected_index]

                            # Check if it's a simulated result
                            is_simulated = hasattr(call, 'simulated') and call.simulated

                            # Display detailed information about the selected call
                            st.markdown("### Tool Call Details")

                            # Show simulated warning if applicable
                            if is_simulated:
                                st.warning("⚠️ This is a simulated result because the actual tool call failed.")

                            st.markdown(f"**Tool:** {call.tool_name}")

                            # Show status based on whether it's simulated
                            if is_simulated:
                                st.markdown("**Status:** ⚠️ Simulated (actual call failed)")
                                # Show the original error if available
                                if hasattr(call, 'error') and call.error:
                                    st.error(f"**Original Error:** {call.error}")
                            else:
                                st.markdown(f"**Success:** {'Yes' if call.success else 'No'}")

                            if call.execution_time:
                                st.markdown(f"**Execution Time:** {call.execution_time:.2f} seconds")

                            st.markdown("**Input:**")
                            st.code(format_json(call.tool_input), language="json")

                            if call.success or is_simulated:
                                st.markdown("**Result:**")
                                st.code(call.content, language="text")
                            else:
                                st.markdown("**Error:**")
                                st.error(call.error)
                else:
                    st.info("No tool calls recorded yet.")

if __name__ == "__main__":
    main()
