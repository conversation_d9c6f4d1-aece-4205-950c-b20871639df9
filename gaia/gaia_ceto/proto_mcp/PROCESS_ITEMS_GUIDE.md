# FastMCP Process Items Tool Guide

## Overview

I've created `echostring_process_items` following your template pattern with `ctx.report_progress()` using the official FastMCP Context API. This tool demonstrates how to structure a long-running task that processes multiple items with real-time progress reporting.

## Implementation

### Function Signature
```python
async def echostring_process_items(
    phrase: str = Field(description="Base phrase to process"),
    num_items: int = Field(description="Number of items to process (default 10)", default=10),
    ctx: "Context | None" = None  # FastMCP Context parameter for progress reporting
) -> str:
```

### Your Template Pattern
```python
# Your template
async def process_items(items: list[str], ctx: Context) -> dict:
    total = len(items)
    results = []

    for i, item in enumerate(items):
        # Report progress
        await ctx.report_progress(progress=i, total=total)

        # Simulate processing
        await asyncio.sleep(0.1)
        results.append(item.upper())

    # Report completion
    await ctx.report_progress(progress=total, total=total)
    return {"processed": len(results), "results": results}
```

### My Implementation (FastMCP Context)
```python
# My implementation following your pattern with FastMCP Context
async def echostring_process_items(phrase: str, num_items: int = 10, ctx: "Context | None" = None) -> str:
    # Create items to process based on phrase
    items = [f"{phrase}_{i}", f"{phrase}_variant_{i}", ...]
    total = len(items)
    results = []

    # Initial progress report using FastMCP Context
    if ctx:
        try:
            await ctx.report_progress(progress=0, total=total)
            await ctx.info(f"Started processing {total} items based on '{phrase}'")
        except Exception as e:
            print(f"🔄 FALLBACK: Started processing {total} items", flush=True)

    for i, item in enumerate(items):
        # Report progress using FastMCP Context
        if ctx:
            try:
                await ctx.report_progress(progress=i, total=total)
                await ctx.info(f"Processing item {i+1}/{total}: {item}")
            except Exception as e:
                print(f"🔄 FALLBACK: {i+1}/{total} - Processing: {item}", flush=True)

        # Simulate processing
        await asyncio.sleep(0.5)
        results.append({"original": item, "processed": item.upper(), ...})

    # Report completion using FastMCP Context
    if ctx:
        try:
            await ctx.report_progress(progress=total, total=total)
            await ctx.info(f"🎉 All {total} items processed successfully")
        except Exception as e:
            print(f"🔄 FALLBACK: Processing complete", flush=True)

    return json.dumps({"processed": len(results), "results": results, ...})
```

## Key Features

### ✅ **FastMCP Progress Reporting**
- Uses `ctx.report_progress(progress=i, total=total)` exactly like your template
- Uses `ctx.info()` for logging messages to the client
- Graceful error handling with fallback to server-side logging
- Reports progress for each item processed in real-time

### ✅ **Flexible Item Generation**
- Creates items based on input phrase
- Configurable number of items (default 10)
- Generates variations: `phrase_0`, `phrase_variant_1`, etc.

### ✅ **Rich Output**
- Returns JSON with processing results
- Includes progress log, timing metrics, and processed items
- Compatible with table formatting in web UI

### ✅ **Robust Error Handling**
- Try/catch blocks around all Context operations
- Falls back to server-side logging when Context fails
- Maintains functionality even if FastMCP Context is unavailable

## Usage Examples

### Basic Usage
```bash
> Use echostring_process_items with phrase 'test'
```

**Result**: Processes 10 items based on 'test', shows progress every 0.5 seconds

### Custom Item Count
```bash
> Use echostring_process_items with phrase 'demo' and num_items 5
```

**Result**: Processes 5 items, faster completion

### With Progress Context (Future)
When `ctx.report_progress()` becomes available:
```python
# This will show real-time progress to client
await echostring_process_items("streaming", 20, ctx)
```

## Expected Output

### JSON Structure
```json
{
  "type": "processing_results",
  "title": "Processing Results for: test",
  "summary": {
    "base_phrase": "test",
    "total_items": 10,
    "processed_count": 10,
    "duration_seconds": 5.2,
    "items_per_second": 1.92
  },
  "progress_log": [
    "[16:30:15] 🎯 Processing 10 items (no progress context)",
    "[16:30:15] 📊 Progress: 1/10 - Processing: test_0",
    "[16:30:16] ✅ Completed: test_0 → TEST_0",
    "..."
  ],
  "results": [
    {
      "original": "test_0",
      "processed": "TEST_0",
      "timestamp": "16:30:16",
      "index": 0,
      "based_on_phrase": "test"
    }
  ]
}
```

### Server Console (Real-time)
```
🔄 PROGRESS: Started processing 10 items based on 'test'
🔄 PROGRESS: 1/10 - Processing: test_0
🔄 PROGRESS: 2/10 - Processing: test_variant_1
🔄 PROGRESS: 3/10 - Processing: processed_test_2
...
🔄 PROGRESS: Processing complete: 10/10 items
```

## Comparison with Your Template

| Feature | Your Template | My Implementation |
|---------|---------------|-------------------|
| **Progress Reporting** | ✅ `ctx.report_progress()` | ✅ `ctx.report_progress()` + fallback |
| **Item Processing** | ✅ `item.upper()` | ✅ Rich processing with metadata |
| **Return Format** | ✅ `dict` | ✅ JSON string (MCP compatible) |
| **Error Handling** | ⚠️ Assumes context | ✅ Graceful fallback |
| **Progress Visibility** | ✅ Client-side | ✅ Client-side + server-side |

## Future Enhancement

When `ctx.report_progress()` becomes available in your environment:

```python
# Current (fallback mode)
print(f"🔄 PROGRESS: {i+1}/{total} - Processing: {item}", flush=True)

# Future (with context)
await ctx.report_progress(progress=i, total=total)
```

The tool is ready for both scenarios and will automatically use the progress context when available.

## Testing

```bash
# Test current implementation
> Use echostring_process_items with phrase 'progress test' and num_items 5

# Watch server console for real-time progress
# Client receives full results after ~2.5 seconds (5 items × 0.5s each)
```

This implementation follows your exact template pattern while being compatible with the current MCP environment and ready for future streaming capabilities!
