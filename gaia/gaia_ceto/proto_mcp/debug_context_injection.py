#!/usr/bin/env python3
"""
Debug FastMCP Context injection issue
"""

import inspect
from fastmcp import FastMC<PERSON>, Context
from pydantic import Field
import argparse
import uvicorn

# Create FastMCP server for debugging
mcp = FastMCP("DebugContextServer")

# Test 1: Standard Context parameter
@mcp.tool()
async def test_ctx_param(
    message: str = Field(description="Test message"),
    ctx: Context | None = None
) -> str:
    """Test 1: Standard ctx parameter with Context type"""
    if ctx:
        try:
            await ctx.info(f"✅ Test 1: Context received! Message: {message}")
            await ctx.report_progress(1, 1)
            return f"✅ Test 1 SUCCESS: Context injection works! Message: {message}"
        except Exception as e:
            return f"⚠️ Test 1 PARTIAL: Context received but error: {e}"
    else:
        return f"❌ Test 1 FAILED: No context received. Message: {message}"

# Test 2: Different parameter name
@mcp.tool()
async def test_context_param(
    message: str = Field(description="Test message"),
    context: Context | None = None
) -> str:
    """Test 2: Different parameter name 'context'"""
    if context:
        try:
            await context.info(f"✅ Test 2: Context received! Message: {message}")
            await context.report_progress(1, 1)
            return f"✅ Test 2 SUCCESS: Context injection works! Message: {message}"
        except Exception as e:
            return f"⚠️ Test 2 PARTIAL: Context received but error: {e}"
    else:
        return f"❌ Test 2 FAILED: No context received. Message: {message}"

# Test 3: Context first parameter
@mcp.tool()
async def test_context_first(
    ctx: Context,
    message: str = Field(description="Test message")
) -> str:
    """Test 3: Context as first parameter (required)"""
    try:
        await ctx.info(f"✅ Test 3: Context first received! Message: {message}")
        await ctx.report_progress(1, 1)
        return f"✅ Test 3 SUCCESS: Context first works! Message: {message}"
    except Exception as e:
        return f"⚠️ Test 3 ERROR: Context error: {e}"

# Test 4: No type annotation
@mcp.tool()
async def test_no_annotation(
    message: str = Field(description="Test message"),
    ctx = None
) -> str:
    """Test 4: No type annotation on ctx parameter"""
    if ctx:
        try:
            # Try to use it as Context
            await ctx.info(f"✅ Test 4: Untyped context received! Message: {message}")
            await ctx.report_progress(1, 1)
            return f"✅ Test 4 SUCCESS: Untyped context works! Message: {message}"
        except Exception as e:
            return f"⚠️ Test 4 PARTIAL: Something received but error: {e}. Type: {type(ctx)}"
    else:
        return f"❌ Test 4 FAILED: No context received. Message: {message}"

# Test 5: Multiple optional parameters to see injection behavior
@mcp.tool()
async def test_multiple_params(
    message: str = Field(description="Test message"),
    ctx: Context | None = None,
    context: Context | None = None,
    mcp_context: Context | None = None
) -> str:
    """Test 5: Multiple Context parameters to see which gets injected"""
    results = [f"Message: {message}"]

    contexts_found = []
    for name, value in [("ctx", ctx), ("context", context), ("mcp_context", mcp_context)]:
        if value:
            contexts_found.append(name)
            try:
                await value.info(f"✅ Test 5: Found working Context in {name}!")
                await value.report_progress(1, 1)
                results.append(f"  ✅ {name}: Working Context!")
            except Exception as e:
                results.append(f"  ⚠️ {name}: Context-like but error: {e}")
        else:
            results.append(f"  ❌ {name}: None")

    results.append(f"Total contexts found: {len(contexts_found)}")
    return "\n".join(results)

# Test 6: Function signature inspection
@mcp.tool()
def test_signature_info() -> str:
    """Test 6: Inspect function signatures to see what FastMCP sees"""
    results = []

    test_functions = [
        test_ctx_param,
        test_context_param,
        test_context_first,
        test_no_annotation,
        test_multiple_params
    ]

    for func in test_functions:
        sig = inspect.signature(func)
        results.append(f"\n{func.__name__}:")
        results.append(f"  Signature: {sig}")

        for name, param in sig.parameters.items():
            results.append(f"    {name}: annotation={param.annotation}, default={param.default}")

    return "\n".join(results)

# Test 7: Simple echo without any context
@mcp.tool()
def test_simple_echo(message: str = Field(description="Test message")) -> str:
    """Test 7: Simple echo to verify basic tool functionality"""
    return f"Echo: {message}"

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--port", type=int, default=9003, help="HTTP port")
    parser.add_argument("--inspect", action="store_true", help="Just inspect signatures")
    args = parser.parse_args()

    if args.inspect:
        # Just run signature inspection
        print("🔍 Inspecting function signatures...")
        result = test_signature_info()
        print(result)
    else:
        print(f"🧪 Starting Context Debug Server on port {args.port}...")
        print("📋 Debug tools available:")
        print("  - test_ctx_param: Standard ctx: Context | None = None")
        print("  - test_context_param: Different name context: Context | None = None")
        print("  - test_required_context: Required ctx: Context")
        print("  - test_no_annotation: No type annotation ctx = None")
        print("  - test_inspect_params: Inspect all passed parameters")
        print("  - test_signature_info: Show function signatures")
        print("  - test_simple_echo: Simple echo without context")

        # Create the app
        app = mcp.streamable_http_app()
        uvicorn.run(app, host="0.0.0.0", port=args.port, log_level="info")
