import asyncio
import sys
from typing import Optional
from contextlib import AsyncExitStack

# Import ClientSession and types from mcp core
from mcp import ClientSession, types
# Import sse_client specifically for SSE transport
from mcp.client.sse import sse_client

from anthropic import Anthropic
from dotenv import load_dotenv

load_dotenv()  # load environment variables from.env

# --- Configuration ---
DEFAULT_SERVER_URL = "http://0.0.0.0:8000/sse" # Default URL for SSE

def debug_messages(label: str, messages):
    # Simple print debugging for message lists
    print(f"\n--- ---")
    for i, message in enumerate(messages):
        print(f"[{i}] Role: {message.get('role', 'N/A')}")
        print(f"    Content: {message.get('content', 'N/A')}")
    print("------------------------------------\n")

def debug_response(label: str, response):
    # Simple print debugging for Anthropic responses
    print(f"\n--- ---")
    # Check if response is likely an Anthropic Messages API response object
    if hasattr(response, 'content') and hasattr(response, 'stop_reason'):
        print(f"Stop Reason: {response.stop_reason}")
        print("Content:")
        for i, content_block in enumerate(response.content):
            print(f"  Block [{i}] Type: {content_block.type}")
            if content_block.type == 'text':
                print(f"    Text: {content_block.text[:100]}...") # Print start of text
            elif content_block.type == 'tool_use':
                print(f"    Tool Name: {content_block.name}")
                print(f"    Tool Input: {content_block.input}")
                print(f"    Tool Use ID: {content_block.id}")
    else:
        # Fallback for other response types
        print(response)
    print("------------------------------------\n")


class MCPClient:
    def __init__(self):
        # Initialize session and client objects
        self.session: Optional = None
        self.exit_stack = AsyncExitStack()
        self.anthropic = Anthropic() # Assumes ANTHROPIC_API_KEY is in env
        self.available_tools = []

    async def connect_to_server(self, server_url: str):
        """Connect to an MCP server via SSE transport.

        Args:
            server_url: The HTTP URL of the server's SSE endpoint.
        """
        print(f"Attempting to connect to MCP server via SSE at: {server_url}")
        try:
            # Establish SSE transport connection using the provided URL
            # sse_client manages the HTTP connection for Server-Sent Events.
            sse_transport = await self.exit_stack.enter_async_context(sse_client(server_url))
            read_stream, write_stream = sse_transport
            print("SSE transport connection established successfully.")

            # Create and initialize the MCP ClientSession using the SSE streams
            # ClientSession manages the MCP protocol logic over the transport.
            self.session = await self.exit_stack.enter_async_context(ClientSession(read_stream, write_stream))
            print("Initializing MCP session (performing handshake)...")
            await self.session.initialize()
            print("MCP session initialized successfully.")

            # List available tools from the connected server
            response = await self.session.list_tools()
            tools = response.tools
            print("\nConnected to server with tools:", [tool.name for tool in tools])

            # Prepare tools in the format expected by Anthropic API
            self.available_tools = []
            for tool in tools:
                self.available_tools.append({
                    "name": tool.name,
                    "description": tool.description,
                    "input_schema": tool.inputSchema
                })

        except ConnectionRefusedError:
            print(f"\nConnection Error: Could not connect to the server at {server_url}.")
            print("Please ensure the MCP server process is running, accessible,")
            print("and listening on the correct address and port with SSE enabled.")
            # Re-raise or handle as appropriate for the application
            raise
        except Exception as e:
            print(f"\nAn unexpected error occurred during connection or initialization: {e}")
            # Consider logging traceback here
            # import traceback
            # traceback.print_exc()
            raise # Re-raise the exception to be caught by the main loop


    async def process_query(self, query: str) -> str:
        """Process a query using Claude and available tools via the MCP session."""
        if not self.session:
            return "Error: Not connected to MCP server."

        messages = [{"role": "user", "content": query}]
        final_response_parts = [] # Store parts of the final response text

        print("\n--- Starting Query Processing ---")
        debug_messages("Initial User Query", messages)
        # Note: Ensure self.available_tools is correctly formatted for Anthropic
        # print("DEBUG: Tools being sent to Anthropic:", self.available_tools)

        try:
            # Initial call to Claude
            response = self.anthropic.messages.create(
                model="claude-3-5-sonnet-20240620", # Use a known compatible model
                max_tokens=1024, # Adjusted token limit
                messages=messages,
                tools=self.available_tools
            )
            debug_response("Initial Claude Response", response)

            # Append initial assistant message(s) to conversation history
            assistant_content_blocks = []
            for content_block in response.content:
                 # Add text blocks immediately to final response
                if content_block.type == 'text':
                    final_response_parts.append(content_block.text)
                    assistant_content_blocks.append({"type": "text", "text": content_block.text})
                elif content_block.type == 'tool_use':
                    # Don't add tool use block to final text yet
                    assistant_content_blocks.append({
                        "type": "tool_use",
                        "id": content_block.id,
                        "name": content_block.name,
                        "input": content_block.input,
                    })

            if assistant_content_blocks:
                 messages.append({"role": "assistant", "content": assistant_content_blocks})

            # Process tool calls if any were requested
            tool_use_blocks = [block for block in response.content if block.type == 'tool_use']

            if tool_use_blocks:
                print(f"\nClaude requested {len(tool_use_blocks)} tool call(s).")
                tool_results_content = [] # Content for the next user message to Claude

                for tool_use in tool_use_blocks:
                    tool_name = tool_use.name
                    tool_input = tool_use.input
                    tool_call_id = tool_use.id # Get the ID for the result

                    print(f"  Calling tool: '{tool_name}' with input: {tool_input}")
                    # Add placeholder text to final response
                    final_response_parts.append(f"\n[Calling tool '{tool_name}'...]")

                    # Execute the tool call via MCP session
                    try:
                        mcp_result: types.CallToolResult = await self.session.call_tool(tool_name, tool_input)
                        print("MCP RESULT: ", mcp_result)
                        # Prepare result content for Claude
                        tool_result_block = {
                            "type": "tool_result",
                            "tool_use_id": tool_call_id,
                        }

                        if mcp_result.isError:
                            print(f"  Tool '{tool_name}' reported an error.")
                            tool_result_block["is_error"] = True
                            # Optionally include error details if available in mcp_result.content
                            # For now, just indicate error
                            tool_result_block["content"] = f"Error executing tool {tool_name}."
                            final_response_parts.append(f"")
                        else:
                            print(f"  Tool '{tool_name}' executed successfully.")
                            # Extract content from MCP result, assuming text for simplicity
                            result_text = ""
                            if mcp_result:
                                result_text = mcp_result.content 
                                # result_text = "\n".join(text_parts)
                                print("RESULT TEXT: ", result_text)
                            tool_result_block["content"] = result_text # Send extracted text back
                            # Append successful tool result text to final response
                            final_response_parts.append(f"\n")

                        tool_results_content.append(tool_result_block)

                    except Exception as tool_call_error:
                        print(f"  Error calling tool '{tool_name}' via MCP: {tool_call_error}")
                        # Report failure back to Claude
                        tool_results_content.append({
                            "type": "tool_result",
                            "tool_use_id": tool_call_id,
                            "is_error": True,
                            "content": f"Client-side error calling tool {tool_name}: {tool_call_error}"
                        })
                        final_response_parts.append(f"[Failed to call tool '{tool_name}'.]")

                print("TOOL RESULTS CONTENT: ", tool_results_content)

                # Send tool results back to Claude
                messages.append({
                    "role": "user",
                    "content": tool_results_content
                })
                debug_messages("Messages After Tool Results", messages)

                # Get Claude's response summarizing tool results
                follow_up_response = self.anthropic.messages.create(
                    model="claude-3-5-sonnet-20240620",
                    max_tokens=1024,
                    messages=messages,
                    # No tools needed for this follow-up, usually
                )
                debug_response("Follow-up Claude Response", follow_up_response)

                # Add Claude's final text response
                for content_block in follow_up_response.content:
                    if content_block.type == 'text':
                        final_response_parts.append(content_block.text)

        except Exception as e:
            print(f"\nError during Anthropic API call or processing: {e}")
            # Consider logging traceback
            return f"An error occurred: {e}"

        print("--- Query Processing Finished ---")
        # Join all collected response parts (initial text, tool placeholders/results, final text)
        return "\n".join(final_response_parts).strip()


    async def chat_loop(self):
        """Run an interactive chat loop."""
        print("\nMCP Client (SSE Mode) Started!")
        print("Type your queries or 'quit' to exit.")

        while True:
            try:
                query = input("\nQuery: ").strip()
                # query = "get_notes for VA and also get_reminders"
                #query = "geo company listing for domain agfunder.com"
                #query= "get_company_categories_matching chemical engineering"

                if not query:
                    continue
                if query.lower() == 'quit':
                    print("Exiting chat loop.")
                    break

                response_text = await self.process_query(query)
                print("\nClaude Response:")
                print(response_text)

                #exit(1)

            except EOFError: # Handle Ctrl+D
                 print("\nExiting...")
                 break
            except KeyboardInterrupt: # Handle Ctrl+C
                 print("\nExiting...")
                 break
            except Exception as e:
                # Catch errors from process_query or input issues
                print(f"\nAn error occurred in the chat loop: {str(e)}")
                # Optionally break or continue based on error severity
                # break

    async def cleanup(self):
        """Clean up resources by closing the AsyncExitStack."""
        print("\nCleaning up resources...")
        await self.exit_stack.aclose()
        print("Cleanup complete.")

async def main():
    # Determine server URL: use command-line arg or default
    server_url = sys.argv[1] if len(sys.argv) > 1 else DEFAULT_SERVER_URL

    client = MCPClient()
    try:
        # Connect using the determined URL
        await client.connect_to_server(server_url)
        # Start the interactive chat
        await client.chat_loop()
    except Exception as e:
         # Catch connection errors or other fatal startup issues
         print(f"\nFatal error during startup or execution: {e}")
         # Error during connect_to_server is already printed there
    finally:
        # Ensure cleanup runs even if errors occur
        await client.cleanup()

if __name__ == "__main__":
    # Ensure sys is imported if not already at top level
    # import sys
    asyncio.run(main())
