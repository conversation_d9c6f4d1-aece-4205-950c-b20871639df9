#!/usr/bin/env python3
"""
Test script to verify that the timeout fix works for echostring_longrunning

This script tests that the long-running tool can complete successfully
with the increased timeout settings.
"""

import asyncio
import time
import sys
import os

# Add the parent directory to the path so we can import the client library
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from proto_mcp_http.mcp_http_clientlib import MC<PERSON><PERSON><PERSON>ib
    HTTP_AVAILABLE = True
except ImportError:
    HTTP_AVAILABLE = False
    print("HTTP client library not available")

try:
    from proto_mcp.mcp_sse_clientlib import MC<PERSON><PERSON><PERSON>ib as SSEMCPClientLib
    SSE_AVAILABLE = True
except ImportError:
    SSE_AVAILABLE = False
    print("SSE client library not available")


async def test_http_timeout():
    """Test the HTTP client with long-running tool"""
    if not HTTP_AVAILABLE:
        print("❌ HTTP client not available, skipping HTTP test")
        return False
    
    print("🔧 Testing HTTP Client with Long-Running Tool")
    print("=" * 50)
    
    client = MCPClientLib()
    
    try:
        # Connect to server
        server_url = "http://0.0.0.0:9000/mcp"
        print(f"Connecting to HTTP server: {server_url}")
        success = await client.connect_to_server(server_url)
        
        if not success:
            print("❌ Failed to connect to HTTP server")
            return False
        
        print("✅ Connected to HTTP server")
        
        # Check if echostring_longrunning is available
        tool_names = [tool['name'] for tool in client.available_tools]
        if 'echostring_longrunning' not in tool_names:
            print("❌ echostring_longrunning tool not available")
            print(f"Available tools: {', '.join(tool_names)}")
            return False
        
        print("✅ echostring_longrunning tool is available")
        
        # Test the long-running tool with increased timeout
        print("\n🚀 Testing echostring_longrunning with 70-second timeout...")
        print("This will take approximately 60 seconds...")
        
        start_time = time.time()
        
        result = await client.process_query(
            query="Use echostring_longrunning with 'timeout test'",
            tool_timeout=70  # 70 second timeout
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n⏱️  Total test duration: {duration:.1f} seconds")
        
        if result["error"]:
            print(f"❌ Error: {result['error']}")
            return False
        else:
            print("✅ Long-running tool completed successfully!")
            
            # Check tool results
            if result["tool_results"]:
                for tool_result in result["tool_results"]:
                    if tool_result.tool_name == "echostring_longrunning":
                        if tool_result.success:
                            print(f"✅ Tool execution time: {tool_result.execution_time:.1f} seconds")
                            # Show first and last few lines of output
                            lines = tool_result.content.split('\n')
                            print(f"📝 Output lines: {len(lines)}")
                            print("📋 First 3 lines:")
                            for line in lines[:3]:
                                print(f"   {line}")
                            print("📋 Last 3 lines:")
                            for line in lines[-3:]:
                                print(f"   {line}")
                        else:
                            print(f"❌ Tool failed: {tool_result.error}")
                            return False
            
            return True
    
    except Exception as e:
        print(f"❌ Exception during HTTP test: {e}")
        return False
    
    finally:
        await client.cleanup()


async def test_sse_timeout():
    """Test the SSE client with long-running tool"""
    if not SSE_AVAILABLE:
        print("❌ SSE client not available, skipping SSE test")
        return False
    
    print("\n🔧 Testing SSE Client with Long-Running Tool")
    print("=" * 50)
    
    client = SSEMCPClientLib()
    
    try:
        # Connect to server
        server_url = "http://0.0.0.0:8000/sse"
        print(f"Connecting to SSE server: {server_url}")
        success = await client.connect_to_server(server_url)
        
        if not success:
            print("❌ Failed to connect to SSE server")
            return False
        
        print("✅ Connected to SSE server")
        
        # Check if echostring_longrunning is available
        tool_names = [tool['name'] for tool in client.available_tools]
        if 'echostring_longrunning' not in tool_names:
            print("❌ echostring_longrunning tool not available")
            print(f"Available tools: {', '.join(tool_names)}")
            return False
        
        print("✅ echostring_longrunning tool is available")
        
        # Test the long-running tool with increased timeout
        print("\n🚀 Testing echostring_longrunning with 70-second timeout...")
        print("This will take approximately 60 seconds...")
        
        start_time = time.time()
        
        result = await client.process_query(
            query="Use echostring_longrunning with 'SSE timeout test'",
            tool_timeout=70  # 70 second timeout
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n⏱️  Total test duration: {duration:.1f} seconds")
        
        if result["error"]:
            print(f"❌ Error: {result['error']}")
            return False
        else:
            print("✅ Long-running tool completed successfully!")
            return True
    
    except Exception as e:
        print(f"❌ Exception during SSE test: {e}")
        return False
    
    finally:
        await client.cleanup()


async def main():
    """Main test function"""
    print("🧪 MCP Long-Running Tool Timeout Fix Test")
    print("=" * 60)
    print("This test verifies that the echostring_longrunning tool")
    print("can complete successfully with the increased timeout settings.")
    print("=" * 60)
    
    # Test HTTP client
    http_success = await test_http_timeout()
    
    # Test SSE client
    sse_success = await test_sse_timeout()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"HTTP Client: {'✅ PASSED' if http_success else '❌ FAILED'}")
    print(f"SSE Client:  {'✅ PASSED' if sse_success else '❌ FAILED'}")
    
    if http_success or sse_success:
        print("\n🎉 At least one client successfully completed the long-running tool!")
        print("The timeout fix is working correctly.")
    else:
        print("\n⚠️  Both clients failed. Check server status and timeout settings.")
    
    print("=" * 60)


if __name__ == "__main__":
    print("⚠️  WARNING: This test will take approximately 2 minutes to complete!")
    print("Make sure both MCP servers are running:")
    print("  - HTTP server: python -m gaia.gaia_ceto.proto_mcp_http.mcp_http_server")
    print("  - SSE server:  python -m gaia.gaia_ceto.proto_mcp.mcp_sse_server")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user (Ctrl+C)")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
