#!/usr/bin/env python3
"""
Test script to verify FastMCP Context injection in tools
"""

import asyncio
import time
from fastmcp import FastMCP, Context
from pydantic import Field

# Create a minimal FastMCP server for testing
mcp = FastMCP("TestContextServer")

@mcp.tool()
async def test_context_tool(
    phrase: str = Field(description="Test phrase to process"),
    num_items: int = Field(description="Number of items to process", default=3),
    ctx: Context | None = None  # FastMCP Context parameter
) -> str:
    """Test tool to verify Context injection"""

    if ctx:
        await ctx.info(f"✅ Context received! Processing '{phrase}' with {num_items} items")

        for i in range(num_items):
            await ctx.report_progress(progress=i, total=num_items)
            await ctx.info(f"Processing item {i+1}/{num_items}")
            await asyncio.sleep(0.5)

        await ctx.report_progress(progress=num_items, total=num_items)
        await ctx.info("🎉 Processing complete!")

        return f"✅ SUCCESS: Context was injected and used for {num_items} items"
    else:
        return f"❌ FAILED: No context received (ctx is None)"

async def test_context_injection():
    """Test if Context is properly injected"""
    print("🧪 Testing FastMCP Context injection...")

    # Test the tool directly (should show no context)
    result1 = await test_context_tool("direct_call", 2, None)
    print(f"Direct call result: {result1}")

    # In a real FastMCP server, the Context would be injected automatically
    # This test shows what the function signature looks like
    import inspect
    sig = inspect.signature(test_context_tool)
    print(f"\nFunction signature: {sig}")

    for name, param in sig.parameters.items():
        print(f"  {name}: {param.annotation} = {param.default}")

    print("\n📋 FastMCP should automatically inject Context when:")
    print("  1. Parameter is named 'ctx' (or any name)")
    print("  2. Parameter has type annotation 'Context' or 'Context | None'")
    print("  3. Tool is called through FastMCP server (not directly)")

    return True

if __name__ == "__main__":
    import sys

    if "--server" in sys.argv:
        # Run as FastMCP server
        print("🚀 Starting FastMCP test server on port 9001...")
        import uvicorn
        app = mcp.streamable_http_app()
        uvicorn.run(app, host="0.0.0.0", port=9001, log_level="info")
    else:
        # Run tests
        try:
            success = asyncio.run(test_context_injection())
            print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}: Context injection test completed")

            print("\n🔧 To test with real server:")
            print("  1. Start server: python test_context_tool.py --server")
            print("  2. Call tool through MCP client")
            print("  3. Check if Context is injected")

        except Exception as e:
            print(f"\n❌ ERROR: {e}")
            import traceback
            traceback.print_exc()
