import asyncio
from mcp import ClientSession, types
from mcp.client.sse import sse_client
import sys # To potentially exit on error

# --- Configuration ---
# Default server URL. Adjust if your FastMCP server runs on a different
# address, port, or uses a different SSE endpoint path.
SERVER_URL = "http://0.0.0.0:8000/sse"

async def call_mcp_tool(session: ClientSession, tool_name: str, tool_args: dict) -> types.CallToolResult | None:
    """
    Calls a specific tool on the connected MCP server and returns the structured result.

    Args:
        session: The active ClientSession object.
        tool_name: The exact name of the tool to call.
        tool_args: A dictionary of arguments for the tool.

    Returns:
        The types.CallToolResult object received from the server, or None if a
        client-side error occurred during the call itself (e.g., network issue).
        Note: If the *server* reports an error executing the tool, the
        CallToolResult object is still returned, and its 'isError' flag will be True.
    """
    print(f"\n--- Calling remote tool '{tool_name}' ---")
    print(f"Arguments: {tool_args}")
    try:
        # 'call_tool' sends the request and returns the result object.[9, 15]
        result: types.CallToolResult = await session.call_tool(
            tool_name,
            tool_args
        )
        print(f"Tool '{tool_name}' call completed.")
        # Return the entire result object, whether it's a success or a server-side error.
        return result

    except Exception as tool_call_error:
        # Catch potential errors during the 'call_tool' operation itself
        # (e.g., network issues during the call, unexpected server response format).
        print(f"Client-side error occurred while calling tool '{tool_name}': {tool_call_error}")
        # Return None to indicate a failure in the call process itself.
        return None

def process_tool_result(tool_name: str, result: types.CallToolResult | None):
    """Helper function to process and print the result from call_mcp_tool."""
    print(f"\n--- Processing result for '{tool_name}' ---")
    if result is None:
        print("Failed to get a result (client-side error during call).")
        return

    # Check if the server reported an error during tool execution.[15]
    if result.isError:
        print("Tool execution resulted in an error reported by the server:")
        # Potentially log or display more error details if available in result
    else:
        print("Tool execution successful. Result content:")

    # The 'content' attribute is a list of content parts (e.g., text, images).
    if result.content:
        for content_part in result.content:
            # Handle text content, the most common type for simple results.
            if isinstance(content_part, types.TextContent) or getattr(content_part, 'type', None) == "text":
                print(f"- Text: {content_part.text}")
            # Placeholder for handling other potential content types (e.g., images)
            else:
                print(f"- Content (type: {getattr(content_part, 'type', 'unknown')}): {content_part}")
    else:
        # This branch might be reached for successful calls that return no content,
        # or for server-side errors that also don't include specific content.
        if not result.isError:
            print("- No content was returned by the tool.")
        else:
            print("- No specific content provided with the server error.")


async def main():
    """Main asynchronous function to connect and call tools."""
    print(f"Attempting to connect to MCP server at: {SERVER_URL}")
    try:
        # 1. Establish SSE transport connection
        async with sse_client(SERVER_URL) as streams:
            read_stream, write_stream = streams
            print("SSE transport connection established successfully.")

            # 2. Create and initialize the MCP ClientSession
            async with ClientSession(read_stream, write_stream) as session:
                print("Initializing MCP session (performing handshake)...")
                await session.initialize()
                print("MCP session initialized successfully.")

                # --- Now use the reusable function to call tools and get results ---

                # 3a. Call the 'get_notes' tool and get the structured result
                get_notes_args = {"state": "VA"} # Example arguments for get_notes
                get_notes_result = await call_mcp_tool(session, "get_notes", get_notes_args)
                # Process the returned result
                process_tool_result("get_notes", get_notes_result)


                # 3b. Call another hypothetical tool (e.g., 'get_weather')
                get_weather_args = {"location": "New York", "unit": "Celsius"}
                get_weather_result = await call_mcp_tool(session, "get_weather", get_weather_args)
                # Process the returned result
                process_tool_result("get_weather", get_weather_result)


                # Add more tool calls and processing here as needed...
                # another_tool_result = await call_mcp_tool(session, "another_tool", {"param": "value"})
                # process_tool_result("another_tool", another_tool_result)


    except ConnectionRefusedError:
        # Specific handling for inability to establish the initial HTTP connection.
        print(f"\nConnection Error: Could not connect to the server at {SERVER_URL}.")
        print("Please ensure the MCP server process is running, accessible,")
        print("and listening on the correct address and port with SSE enabled.")
        sys.exit(1) # Exit indicating failure
    except Exception as e:
        # Catch any other unexpected errors during connection or session setup.
        print(f"\nAn unexpected error occurred: {e}")
        # Consider logging the full traceback here for debugging complex issues.
        # import traceback
        # traceback.print_exc()
        sys.exit(1) # Exit indicating failure

if __name__ == "__main__":
    try:
        # Standard way to run the main async function.
        asyncio.run(main())
    except KeyboardInterrupt:
        # Allow graceful exit if the user interrupts the script (Ctrl+C).
        print("\nClient execution cancelled by user.")

