#!/usr/bin/env python3
"""
Demo script for the echostring_longrunning tool

This script demonstrates how to use the echostring_longrunning tool
and shows real-time progress monitoring.
"""

import asyncio
import time
from mcp_tools import echostring_longrunning


async def demo_with_live_progress():
    """Demonstrate the long-running tool with live progress updates"""
    print("🚀 MCP Long-Running Task Demo")
    print("=" * 50)
    print("This demo shows how the echostring_longrunning tool works.")
    print("The task will take approximately 60 seconds to complete.")
    print("You'll see live progress updates every 10 seconds.")
    print("=" * 50)
    
    # Get user input
    phrase = input("\nEnter a phrase for the long-running task (or press Enter for 'Demo'): ").strip()
    if not phrase:
        phrase = "Demo"
    
    print(f"\n🎯 Starting long-running task with phrase: '{phrase}'")
    print("⏱️  Expected duration: ~60 seconds")
    print("📊 Progress updates every 10 seconds")
    print("-" * 50)
    
    # Start the task
    task = asyncio.create_task(echostring_longrunning(phrase))
    start_time = time.time()
    
    # Monitor progress
    progress_count = 0
    while not task.done():
        await asyncio.sleep(10)
        progress_count += 1
        elapsed = time.time() - start_time
        
        # Calculate estimated completion
        estimated_total = 60
        progress_percent = min((elapsed / estimated_total) * 100, 100)
        
        print(f"📈 Progress Update #{progress_count}")
        print(f"   ⏰ Elapsed: {elapsed:.1f}s")
        print(f"   📊 Progress: ~{progress_percent:.0f}%")
        print(f"   🔄 Status: Task still running...")
        print("-" * 30)
        
        # Safety timeout
        if elapsed > 70:
            print("⚠️  Safety timeout reached!")
            task.cancel()
            break
    
    # Get the result
    try:
        if not task.cancelled():
            result = await task
            end_time = time.time()
            total_duration = end_time - start_time
            
            print("✅ TASK COMPLETED SUCCESSFULLY!")
            print("=" * 50)
            print(f"🎉 Total Duration: {total_duration:.1f} seconds")
            print(f"📝 Phrase Used: '{phrase}'")
            
            # Show summary of the output
            lines = result.split('\n')
            print(f"📄 Output Lines: {len(lines)}")
            
            print("\n📋 Task Summary:")
            print("   🏁 Start:", lines[0] if lines else "N/A")
            print("   🎯 End:", lines[-1] if lines else "N/A")
            
            # Ask if user wants to see full output
            show_full = input("\n🤔 Show full output? (y/N): ").strip().lower()
            if show_full in ['y', 'yes']:
                print("\n📜 FULL OUTPUT:")
                print("=" * 60)
                print(result)
                print("=" * 60)
            
            return True
        else:
            print("❌ Task was cancelled due to timeout")
            return False
            
    except Exception as e:
        print(f"❌ Error occurred: {e}")
        return False


async def demo_quick_test():
    """Quick demo that cancels after a few seconds to show the start"""
    print("\n" + "=" * 50)
    print("🔬 Quick Test Demo")
    print("=" * 50)
    print("This will start the long-running task and cancel it after 5 seconds")
    print("to show you how it begins without waiting the full 60 seconds.")
    print("-" * 50)
    
    phrase = "QuickTest"
    print(f"🎯 Starting task with phrase: '{phrase}'")
    
    task = asyncio.create_task(echostring_longrunning(phrase))
    
    # Let it run for 5 seconds
    await asyncio.sleep(5)
    
    # Cancel the task
    task.cancel()
    
    try:
        await task
    except asyncio.CancelledError:
        print("✅ Quick test completed - task started successfully!")
        print("   (Task was cancelled after 5 seconds as planned)")
    except Exception as e:
        print(f"❌ Error in quick test: {e}")


def print_usage_info():
    """Print information about how to use the tool in practice"""
    print("\n" + "=" * 60)
    print("📚 USAGE INFORMATION")
    print("=" * 60)
    print("The echostring_longrunning tool can be used in several ways:")
    print()
    print("1. 🌐 Web UI (chat_app.html):")
    print("   - Ask Claude: 'Use echostring_longrunning with \"my phrase\"'")
    print("   - The tool will run and show progress in the chat")
    print()
    print("2. 🖥️  Terminal (chat_term.py):")
    print("   - Start chat_term.py with MCP provider")
    print("   - Ask Claude to use the echostring_longrunning tool")
    print()
    print("3. 🔧 Direct MCP Server:")
    print("   - Connect to MCP server (SSE or HTTP)")
    print("   - Call the tool directly via MCP protocol")
    print()
    print("4. 🧪 Testing:")
    print("   - Use test_echostring_longrunning.py for comprehensive testing")
    print("   - Use this demo script for interactive demonstrations")
    print()
    print("⚠️  Note: The tool takes ~60 seconds to complete!")
    print("   Make sure your MCP client has appropriate timeouts set.")
    print("=" * 60)


async def main():
    """Main demo function"""
    print("🎭 MCP Long-Running Task Demonstration")
    print("=" * 60)
    
    while True:
        print("\nChoose a demo option:")
        print("1. 🚀 Full Demo (60 seconds)")
        print("2. 🔬 Quick Test (5 seconds)")
        print("3. 📚 Usage Information")
        print("4. 🚪 Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            await demo_with_live_progress()
        elif choice == "2":
            await demo_quick_test()
        elif choice == "3":
            print_usage_info()
        elif choice == "4":
            print("\n👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1, 2, 3, or 4.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  Demo interrupted by user (Ctrl+C)")
        print("👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
