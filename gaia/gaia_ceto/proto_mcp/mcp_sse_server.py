from mcp.server.fastmcp import Fast<PERSON><PERSON>
#from mcp import mcp_error
import gaia
from pydantic import Field
from gaia.gaia_frames import gaia_frames
import pandas as pd
import json
# Initialize FastMCP server
mcp = FastMCP("gaia_mcp_server")


org_frame_cols={}

@mcp.tool()
async def echostring( phrase: str=Field(description="Phrase to echo", )) -> str:
    """Echo a string

    Args:
        phrase: the phrase to echo
    """
    # label_cols is a dict of label_name -> list of column names
    # we want to return this as single string in JSON format
    return phrase + ", " + phrase + ", " + phrase + " you weasly wabbit... "


@mcp.tool()
async def get_company_categories_matching( query: str=Field(description="The domain of the company to search for", )) -> str:
    """Get a listing of companies matching the search criteria.

    Args:
        name: The name of the company to search for
    """
    # label_cols is a dict of label_name -> list of column names
    # we want to return this as single string in JSON format
    return "Consider only the most closely related categories. " + json.dumps(org_frame_cols)



@mcp.tool()
async def get_llm_completion( sort_criteria: str=Field(description="The criteria for sorting"),) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.
    Common sort_criteria:
    total_funding_usd_mm
    num_funding_rounds
    last_funding_on
    founded_on
    """
    #breakpoint()

    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        cols=['uuid',sort_criteria], 
        #nrows=10        
    )   
    co = co.sort_values(by=sort_criteria, ascending=False)
    uuids = co.uuid.head(2).to_list()

    uuids_str = [f"'{u}'" for u in uuids]
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=f"uuid in ({','.join( uuids_str )})" , )
    co = co.sort_values(by=sort_criteria, ascending=False)
    print(co.head(2))
    #co = co[co.name.str.contains(name)]
    return co.head(2).to_string()



@mcp.tool()
async def get_top_companies( sort_criteria: str=Field(description="The criteria for sorting"),) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.
    Common sort_criteria:
    total_funding_usd_mm
    num_funding_rounds
    last_funding_on
    founded_on
    """
    #breakpoint()

    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        cols=['uuid',sort_criteria], 
        #nrows=10        
    )   
    co = co.sort_values(by=sort_criteria, ascending=False)
    uuids = co.uuid.head(2).to_list()

    uuids_str = [f"'{u}'" for u in uuids]
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=f"uuid in ({','.join( uuids_str )})" , )
    co = co.sort_values(by=sort_criteria, ascending=False)
    print(co.head(2))
    #co = co[co.name.str.contains(name)]
    return co.head(2).to_string()



@mcp.tool()
async def get_company_listing( domain: str=Field(description="The domain of the company to search for", default=None),  name: str= Field(description="The name of the company to search for", default=None),) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.

    Args:
        name: The name of the company to search for
    """
    #breakpoint()
    where_clauses= []
    if domain:
        where_clauses.append(f"domain='{domain}'")
    if name:
        where_clauses.append(f"name like '{name}%'")

    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=' and '.join(where_clauses)
    )
    print(co.head())
    #co = co[co.name.str.contains(name)]
    return co.to_string()





'''
Gaia tools:

- agsearch contexts
- airtable records
- company search
- investment reporting
- investor match

'''

from gaia.config import subsector_definitions

if __name__ == "__main__":
    # Initialize and run the server
    #breakpoint()

    if 0:
        #cc=get_top_companies(sort_criteria="total_funding_usd_mm")
        #print(cc)
        csv = "/G_gaia/frames/pred/org_class-_-class__cb_cat_groups-_-CB-_-modernbert-base__001__class__cb_cat_groups__03/head.csv"
        df = pd.read_csv(csv)
        print(df.head())
        # get column names
        cols = list(df.columns)
        print(cols)
        this_label_cols = list(set(cols)-set(['uuid']))
        org_frame_cols["cb_cat_groups"] = this_label_cols

        # get long names from gaia.config
    if 0:
        config = subsector_definitions.get_config_classes("class__cb_cat_groups")
        print(config)

    if 0:
        ret = get_company_listing(domain="agfunder.com")
        print(ret)

    mcp.run(transport='sse')
