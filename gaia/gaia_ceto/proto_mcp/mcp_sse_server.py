from mcp.server.fastmcp import FastMCP
import argparse
import uvicorn
import pandas as pd

# Import shared tools
from gaia.gaia_ceto.proto_mcp.mcp_tools import (
    echostring,
    echostring_table,
    echostring_process_items,
    echostring_longrunning,
    echostring_streaming_progress,
    echostring_quick_stage,
    get_company_categories_matching,
    get_llm_completion,
    get_top_companies,
    get_company_listing,
    org_frame_cols
)

# Initialize FastMCP server
mcp = FastMCP("gaia_mcp_server")

# Register the shared tools with the MCP server
mcp.tool()(echostring)
mcp.tool()(echostring_table)
mcp.tool()(echostring_process_items)
mcp.tool()(echostring_longrunning)
mcp.tool()(echostring_longrunning_streaming)
mcp.tool()(echostring_streaming_progress)
mcp.tool()(echostring_quick_stage)
mcp.tool()(get_company_categories_matching)
mcp.tool()(get_llm_completion)
mcp.tool()(get_top_companies)
mcp.tool()(get_company_listing)

'''
Gaia tools:

- agsearch contexts
- airtable records
- company search
- investment reporting
- investor match

'''

from gaia.config import subsector_definitions

if __name__ == "__main__":
    # Initialize and run the server
    parser = argparse.ArgumentParser()
    parser.add_argument("--port", type=int, default=8000, help="HTTP port")
    args = parser.parse_args()

    if 0:
        # Example usage of the tools
        # cc = get_top_companies(sort_criteria="total_funding_usd_mm")
        # print(cc)
        csv = "/G_gaia/frames/pred/org_class-_-class__cb_cat_groups-_-CB-_-modernbert-base__001__class__cb_cat_groups__03/head.csv"
        df = pd.read_csv(csv)
        print(df.head())
        # get column names
        cols = list(df.columns)
        print(cols)
        this_label_cols = list(set(cols) - set(['uuid']))
        org_frame_cols["cb_cat_groups"] = this_label_cols

        # get long names from gaia.config
    if 0:
        config = subsector_definitions.get_config_classes("class__cb_cat_groups")
        print(config)

    if 0:
        ret = get_company_listing(domain="agfunder.com")
        print(ret)

    # Create the SSE app and run the server
    app = mcp.sse_app()
    uvicorn.run(app, host="0.0.0.0", port=args.port, log_level="info")
