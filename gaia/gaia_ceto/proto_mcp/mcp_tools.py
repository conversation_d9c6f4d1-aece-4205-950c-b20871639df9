"""
MCP Tools - Shared Functions

This module contains the common tool functions used by both the SSE and HTTP MCP servers.
By centralizing these functions here, we avoid code duplication and ensure consistency
between the different server implementations.
"""

import gaia
from pydantic import Field
from gaia.gaia_frames import gaia_frames
import pandas as pd
import json
import asyncio
import time

# Global variable for organization frame columns
org_frame_cols = {}


async def echostring(phrase: str = Field(description="Phrase to echo")) -> str:
    """Echo a string

    Args:
        phrase: the phrase to echo
    """
    return phrase + ", " + phrase + ", " + phrase + " you weasly wabbit... "


async def echostring_table(phrase: str = Field(description="Phrase to echo in table format")) -> str:
    """Echo a string and return it as a table in JSON format

    Args:
        phrase: the phrase to echo in table format

    Returns:
        JSON string containing table data with headers and rows
    """
    # Create sample table data based on the input phrase
    table_data = {
        "type": "table",
        "headers": ["Column 1", "Column 2", "Column 3"],
        "rows": [
            [phrase, f"{phrase}_modified", f"{phrase}_final"],
            ["Row 2 Data", "More data", "Even more"],
            ["Sample", "Table", "Content"],
            [f"Echo: {phrase}", "Repeated", "Again"]
        ],
        "title": f"Echo Table for: {phrase}"
    }

    return json.dumps(table_data)


async def echostring_longrunning(phrase: str = Field(description="Phrase to echo during a long-running task")) -> str:
    """Echo a string while simulating a long-running task with multiple stages

    This function simulates a 60-second long-running task with multiple stages,
    providing progress updates and intermediate outputs. Useful for testing
    how the MCP system handles long-running operations.

    Args:
        phrase: the phrase to echo during the long-running task

    Returns:
        A detailed log of the long-running task with timestamps and progress
    """
    start_time = time.time()
    output_lines = []

    # Stage 1: Initialization (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Starting long-running task with phrase: '{phrase}'")
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 1/6: Initializing...")
    await asyncio.sleep(5)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Setting up environment for '{phrase}'")
    await asyncio.sleep(5)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Initialization complete")

    # Stage 2: Data Processing (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 2/6: Processing data...")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Analyzing '{phrase}' (25% complete)")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Processing variations of '{phrase}' (50% complete)")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Data processing complete")

    # Stage 3: Computation (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 3/6: Running computations...")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Computing echo patterns for '{phrase}'")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Optimizing output format")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Computation complete")

    # Stage 4: Validation (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 4/6: Validating results...")
    await asyncio.sleep(2)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Checking integrity of '{phrase}' processing")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Running quality assurance tests")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Validation complete")

    # Stage 5: Finalization (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 5/6: Finalizing output...")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Formatting final result for '{phrase}'")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Preparing delivery")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Finalization complete")

    # Stage 6: Completion (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 6/6: Completing task...")
    await asyncio.sleep(2)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Generating final echo: '{phrase}, {phrase}, {phrase}'")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Cleaning up resources")
    await asyncio.sleep(4)

    # Final summary
    end_time = time.time()
    duration = end_time - start_time
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Task completed successfully!")
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Total duration: {duration:.1f} seconds")
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Final result: {phrase}, {phrase}, {phrase} - long-running task complete!")

    return "\n".join(output_lines)


async def get_company_categories_matching(query: str = Field(description="The domain of the company to search for")) -> str:
    """Get a listing of companies matching the search criteria.

    Args:
        query: The domain of the company to search for
    """
    return "Consider only the most closely related categories. " + json.dumps(org_frame_cols)


async def get_llm_completion(sort_criteria: str = Field(description="The criteria for sorting")) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.
    Common sort_criteria:
    total_funding_usd_mm
    num_funding_rounds
    last_funding_on
    founded_on
    """
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        cols=['uuid', sort_criteria],
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    uuids = co.uuid.head(2).to_list()

    uuids_str = [f"'{u}'" for u in uuids]
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=f"uuid in ({','.join(uuids_str)})",
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    print(co.head(2))
    return co.head(2).to_string()


async def get_top_companies(sort_criteria: str = Field(description="The criteria for sorting")) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.
    Common sort_criteria:
    total_funding_usd_mm
    num_funding_rounds
    last_funding_on
    founded_on
    """
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        cols=['uuid', sort_criteria],
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    uuids = co.uuid.head(2).to_list()

    uuids_str = [f"'{u}'" for u in uuids]
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=f"uuid in ({','.join(uuids_str)})",
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    print(co.head(2))
    return co.head(2).to_string()


async def get_company_listing(
    domain: str = Field(description="The domain of the company to search for", default=None),
    name: str = Field(description="The name of the company to search for", default=None)
) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.

    Args:
        domain: The domain of the company to search for
        name: The name of the company to search for
    """
    where_clauses = []
    if domain:
        where_clauses.append(f"domain='{domain}'")
    if name:
        where_clauses.append(f"name like '{name}%'")

    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=' and '.join(where_clauses)
    )
    print(co.head())
    return co.to_string()
