"""
MCP Tools - Shared Functions

This module contains the common tool functions used by both the SSE and HTTP MCP servers.
By centralizing these functions here, we avoid code duplication and ensure consistency
between the different server implementations.
"""

import gaia
from pydantic import Field
from gaia.gaia_frames import gaia_frames
import pandas as pd
import json
import asyncio
import time

# Global variable for organization frame columns
org_frame_cols = {}


async def echostring(phrase: str = Field(description="Phrase to echo")) -> str:
    """Echo a string

    Args:
        phrase: the phrase to echo
    """
    return phrase + ", " + phrase + ", " + phrase + " you weasly wabbit... "


async def echostring_table(phrase: str = Field(description="Phrase to echo in table format")) -> str:
    """Echo a string and return it as a table in JSON format

    Args:
        phrase: the phrase to echo in table format

    Returns:
        JSON string containing table data with headers and rows
    """
    # Create sample table data based on the input phrase
    table_data = {
        "type": "table",
        "headers": ["Column 1", "Column 2", "Column 3"],
        "rows": [
            [phrase, f"{phrase}_modified", f"{phrase}_final"],
            ["Row 2 Data", "More data", "Even more"],
            ["Sample", "Table", "Content"],
            [f"Echo: {phrase}", "Repeated", "Again"]
        ],
        "title": f"Echo Table for: {phrase}"
    }

    return json.dumps(table_data)


async def echostring_longrunning(phrase: str = Field(description="Phrase to echo during a long-running task")) -> str:
    """Echo a string while simulating a long-running task with multiple stages

    This function simulates a 60-second long-running task with multiple stages,
    providing progress updates and intermediate outputs. Useful for testing
    how the MCP system handles long-running operations.

    Args:
        phrase: the phrase to echo during the long-running task

    Returns:
        A detailed log of the long-running task with timestamps and progress
    """
    start_time = time.time()
    output_lines = []

    # Stage 1: Initialization (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Starting long-running task with phrase: '{phrase}'")
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 1/6: Initializing...")
    await asyncio.sleep(5)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Setting up environment for '{phrase}'")
    await asyncio.sleep(5)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Initialization complete")

    # Stage 2: Data Processing (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 2/6: Processing data...")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Analyzing '{phrase}' (25% complete)")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Processing variations of '{phrase}' (50% complete)")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Data processing complete")

    # Stage 3: Computation (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 3/6: Running computations...")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Computing echo patterns for '{phrase}'")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Optimizing output format")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Computation complete")

    # Stage 4: Validation (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 4/6: Validating results...")
    await asyncio.sleep(2)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Checking integrity of '{phrase}' processing")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Running quality assurance tests")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Validation complete")

    # Stage 5: Finalization (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 5/6: Finalizing output...")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Formatting final result for '{phrase}'")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Preparing delivery")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Finalization complete")

    # Stage 6: Completion (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 6/6: Completing task...")
    await asyncio.sleep(2)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Generating final echo: '{phrase}, {phrase}, {phrase}'")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Cleaning up resources")
    await asyncio.sleep(4)

    # Final summary
    end_time = time.time()
    duration = end_time - start_time
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Task completed successfully!")
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Total duration: {duration:.1f} seconds")
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Final result: {phrase}, {phrase}, {phrase} - long-running task complete!")

    return "\n".join(output_lines)


async def echostring_longrunning_streaming(phrase: str = Field(description="Phrase to echo with streaming progress updates")) -> str:
    """Echo a string while simulating a long-running task with streaming progress updates

    This function simulates a 60-second long-running task but provides incremental
    output by yielding progress updates. Note: MCP protocol limitations mean this
    still returns all output at once, but demonstrates how to structure streaming output.

    Args:
        phrase: the phrase to echo during the long-running task

    Returns:
        A detailed log of the long-running task with timestamps and progress
    """
    start_time = time.time()
    output_lines = []

    def add_output(message):
        """Add a message to output and print it immediately for terminal visibility"""
        timestamp = time.strftime('%H:%M:%S')
        formatted_message = f"[{timestamp}] {message}"
        output_lines.append(formatted_message)
        # Print immediately for terminal feedback (this won't show in web UI)
        print(f"🔄 PROGRESS: {formatted_message}")
        return formatted_message

    # Stage 1: Initialization (10 seconds)
    add_output(f"Starting streaming long-running task with phrase: '{phrase}'")
    add_output("Stage 1/6: Initializing...")
    await asyncio.sleep(5)
    add_output(f"- Setting up environment for '{phrase}'")
    await asyncio.sleep(5)
    add_output("- Initialization complete")

    # Stage 2: Data Processing (10 seconds)
    add_output("Stage 2/6: Processing data...")
    await asyncio.sleep(3)
    add_output(f"- Analyzing '{phrase}' (25% complete)")
    await asyncio.sleep(4)
    add_output(f"- Processing variations of '{phrase}' (50% complete)")
    await asyncio.sleep(3)
    add_output("- Data processing complete")

    # Stage 3: Computation (10 seconds)
    add_output("Stage 3/6: Running computations...")
    await asyncio.sleep(4)
    add_output(f"- Computing echo patterns for '{phrase}'")
    await asyncio.sleep(3)
    add_output("- Optimizing output format")
    await asyncio.sleep(3)
    add_output("- Computation complete")

    # Stage 4: Validation (10 seconds)
    add_output("Stage 4/6: Validating results...")
    await asyncio.sleep(2)
    add_output(f"- Checking integrity of '{phrase}' processing")
    await asyncio.sleep(4)
    add_output("- Running quality assurance tests")
    await asyncio.sleep(4)
    add_output("- Validation complete")

    # Stage 5: Finalization (10 seconds)
    add_output("Stage 5/6: Finalizing output...")
    await asyncio.sleep(3)
    add_output(f"- Formatting final result for '{phrase}'")
    await asyncio.sleep(4)
    add_output("- Preparing delivery")
    await asyncio.sleep(3)
    add_output("- Finalization complete")

    # Stage 6: Completion (10 seconds)
    add_output("Stage 6/6: Completing task...")
    await asyncio.sleep(2)
    add_output(f"- Generating final echo: '{phrase}, {phrase}, {phrase}'")
    await asyncio.sleep(4)
    add_output("- Cleaning up resources")
    await asyncio.sleep(4)

    # Final summary
    end_time = time.time()
    duration = end_time - start_time
    add_output("Task completed successfully!")
    add_output(f"Total duration: {duration:.1f} seconds")
    add_output(f"Final result: {phrase}, {phrase}, {phrase} - streaming long-running task complete!")

    return "\n".join(output_lines)


async def echostring_quick_stage(
    phrase: str = Field(description="Phrase to process through all 6 stages"),
    duration: int = Field(description="Duration per stage in seconds (default 10)", default=10)
) -> str:
    """Execute all 6 stages of a multi-stage long-running task with incremental progress

    This tool automatically runs all 6 stages of the long-running task, providing
    incremental progress updates after each stage completes. Each stage takes
    approximately 10 seconds (or the specified duration) and shows immediate results.

    Args:
        phrase: The phrase to process through all stages
        duration: How long each stage should take (default 10 seconds)

    Returns:
        Complete log of all 6 stages with timestamps and progress updates
    """
    stage_names = [
        "Initialization",
        "Data Processing",
        "Computation",
        "Validation",
        "Finalization",
        "Completion"
    ]

    all_output_lines = []
    overall_start_time = time.time()

    all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] 🎯 Starting Multi-Stage Task with phrase: '{phrase}'")
    all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] 📋 Will execute 6 stages, {duration} seconds each")
    all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] ⏱️  Estimated total time: {duration * 6} seconds")
    all_output_lines.append("")

    # Execute all 6 stages
    for stage in range(1, 7):
        stage_name = stage_names[stage - 1]
        stage_start_time = time.time()

        # Stage header
        all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] 🚀 Starting Stage {stage}/6: {stage_name}")
        all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] Processing phrase: '{phrase}'")

        # Stage-specific processing
        if stage == 1:  # Initialization
            await asyncio.sleep(duration // 2)
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Setting up environment for '{phrase}'")
            await asyncio.sleep(duration // 2)
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Environment ready")

        elif stage == 2:  # Data Processing
            await asyncio.sleep(duration // 3)
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Analyzing '{phrase}' (33% complete)")
            await asyncio.sleep(duration // 3)
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Processing variations (66% complete)")
            await asyncio.sleep(duration // 3)
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Data analysis complete")

        elif stage == 3:  # Computation
            await asyncio.sleep(duration // 2)
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Computing echo patterns for '{phrase}'")
            await asyncio.sleep(duration // 2)
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Optimization complete")

        elif stage == 4:  # Validation
            await asyncio.sleep(duration // 2)
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Validating '{phrase}' processing")
            await asyncio.sleep(duration // 2)
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Quality checks passed")

        elif stage == 5:  # Finalization
            await asyncio.sleep(duration // 2)
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Formatting results for '{phrase}'")
            await asyncio.sleep(duration // 2)
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Output prepared")

        elif stage == 6:  # Completion
            await asyncio.sleep(duration // 2)
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Generating final echo: '{phrase}, {phrase}, {phrase}'")
            await asyncio.sleep(duration // 2)
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Task cleanup complete")

        # Stage completion
        stage_end_time = time.time()
        stage_duration = stage_end_time - stage_start_time

        all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] ✅ Stage {stage}/6 Complete: {stage_name}")
        all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage duration: {stage_duration:.1f} seconds")

        # Progress indicator
        progress_percent = (stage / 6) * 100
        all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] 📊 Overall progress: {progress_percent:.0f}% ({stage}/6 stages)")
        all_output_lines.append("")  # Empty line for readability

    # Final summary
    overall_end_time = time.time()
    total_duration = overall_end_time - overall_start_time

    all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] 🎉 ALL STAGES COMPLETE!")
    all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] Total duration: {total_duration:.1f} seconds")
    all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] Final result: {phrase}, {phrase}, {phrase} - multi-stage task complete!")

    return "\n".join(all_output_lines)


async def get_company_categories_matching(query: str = Field(description="The domain of the company to search for")) -> str:
    """Get a listing of companies matching the search criteria.

    Args:
        query: The domain of the company to search for
    """
    return "Consider only the most closely related categories. " + json.dumps(org_frame_cols)


async def get_llm_completion(sort_criteria: str = Field(description="The criteria for sorting")) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.
    Common sort_criteria:
    total_funding_usd_mm
    num_funding_rounds
    last_funding_on
    founded_on
    """
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        cols=['uuid', sort_criteria],
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    uuids = co.uuid.head(2).to_list()

    uuids_str = [f"'{u}'" for u in uuids]
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=f"uuid in ({','.join(uuids_str)})",
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    print(co.head(2))
    return co.head(2).to_string()


async def get_top_companies(sort_criteria: str = Field(description="The criteria for sorting")) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.
    Common sort_criteria:
    total_funding_usd_mm
    num_funding_rounds
    last_funding_on
    founded_on
    """
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        cols=['uuid', sort_criteria],
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    uuids = co.uuid.head(2).to_list()

    uuids_str = [f"'{u}'" for u in uuids]
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=f"uuid in ({','.join(uuids_str)})",
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    print(co.head(2))
    return co.head(2).to_string()


async def get_company_listing(
    domain: str = Field(description="The domain of the company to search for", default=None),
    name: str = Field(description="The name of the company to search for", default=None)
) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.

    Args:
        domain: The domain of the company to search for
        name: The name of the company to search for
    """
    where_clauses = []
    if domain:
        where_clauses.append(f"domain='{domain}'")
    if name:
        where_clauses.append(f"name like '{name}%'")

    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=' and '.join(where_clauses)
    )
    print(co.head())
    return co.to_string()
