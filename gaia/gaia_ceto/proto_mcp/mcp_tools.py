"""
MCP Tools - Shared Functions

This module contains the common tool functions used by both the SSE and HTTP MCP servers.
By centralizing these functions here, we avoid code duplication and ensure consistency
between the different server implementations.
"""

import gaia
from pydantic import Field
from gaia.gaia_frames import gaia_frames
import pandas as pd
import json
import asyncio
import time

# Import FastMCP Context for type hints (use the same import as debug server)
from fastmcp import FastMCP, Context

# Global variable for organization frame columns
org_frame_cols = {}


async def echostring(phrase: str = Field(description="Phrase to echo")) -> str:
    """Echo a string

    Args:
        phrase: the phrase to echo
    """
    return phrase + ", " + phrase + ", " + phrase + " you weasly wabbit... "


async def echostring_table(phrase: str = Field(description="Phrase to echo in table format")) -> str:
    """Echo a string and return it as a table in JSON format

    Args:
        phrase: the phrase to echo in table format

    Returns:
        JSON string containing table data with headers and rows
    """
    # Create sample table data based on the input phrase
    table_data = {
        "type": "table",
        "headers": ["Column 1", "Column 2", "Column 3"],
        "rows": [
            [phrase, f"{phrase}_modified", f"{phrase}_final"],
            ["Row 2 Data", "More data", "Even more"],
            ["Sample", "Table", "Content"],
            [f"Echo: {phrase}", "Repeated", "Again"]
        ],
        "title": f"Echo Table for: {phrase}"
    }

    return json.dumps(table_data)



async def echostring_process_items(
    phrase: str = Field(description="Base phrase to process"),
    num_items: int = Field(description="Number of items to process (default 10)", default=10),
    ctx: Context | None = None  # FastMCP Context parameter for progress reporting
) -> str:
    """Process a list of items with progress updates using FastMCP Context

    This function demonstrates long-running task processing with progress reporting
    using the ctx.report_progress() pattern from FastMCP. It processes multiple
    variations of the input phrase and reports progress throughout.

    Based on the FastMCP template:
    async def process_items(items: list[str], ctx: Context) -> dict:
        for i, item in enumerate(items):
            await ctx.report_progress(progress=i, total=total)
            # ... process item ...
        await ctx.report_progress(progress=total, total=total)

    Args:
        phrase: the base phrase to process
        num_items: number of items to process (default 10)
        ctx: FastMCP Context for progress reporting (when available)

    Returns:
        JSON string containing processing results and progress log
    """
    start_time = time.time()

    # Create items to process based on the phrase
    items = []
    for idx in range(num_items):
        if idx % 4 == 0:
            items.append(f"{phrase}_{idx}")
        elif idx % 4 == 1:
            items.append(f"{phrase}_variant_{idx}")
        elif idx % 4 == 2:
            items.append(f"processed_{phrase}_{idx}")
        else:
            items.append(f"{phrase}_final_{idx}")

    # Ensure we have exactly num_items
    items = items[:num_items]

    total = len(items)
    results = []
    progress_log = []

    # Initial progress report using FastMCP Context
    if ctx:
        try:
            await ctx.report_progress(progress=0, total=total)
            await ctx.info(f"Started processing {total} items based on '{phrase}'")
            progress_log.append(f"[{time.strftime('%H:%M:%S')}] ✅ FastMCP Context: Started processing {total} items")
            print(f"🚀 STARTED: Processing {total} items based on '{phrase}' (FastMCP Context)", flush=True)
        except Exception as e:
            progress_log.append(f"[{time.strftime('%H:%M:%S')}] ⚠️ Context error: {e}")
            print(f"🔄 FALLBACK: Started processing {total} items based on '{phrase}'", flush=True)
    else:
        progress_log.append(f"[{time.strftime('%H:%M:%S')}] 🎯 Processing {total} items (no FastMCP context)")
        print(f"🔄 PROGRESS: Started processing {total} items based on '{phrase}'", flush=True)

    # Process each item with progress updates
    for i, item in enumerate(items):
        # Report progress using FastMCP Context
        if ctx:
            try:
                await ctx.report_progress(progress=i, total=total)
                await ctx.info(f"Processing item {i+1}/{total}: {item}")
                progress_log.append(f"[{time.strftime('%H:%M:%S')}] ✅ FastMCP: Processing {i+1}/{total}: {item}")
                print(f"📊 PROGRESS: {i+1}/{total} - Processing: {item}", flush=True)
            except Exception as e:
                progress_log.append(f"[{time.strftime('%H:%M:%S')}] ⚠️ Context error: {e}")
                print(f"🔄 FALLBACK: {i+1}/{total} - Processing: {item}", flush=True)
        else:
            progress_log.append(f"[{time.strftime('%H:%M:%S')}] 📊 Progress: {i+1}/{total} - Processing: {item}")
            print(f"🔄 PROGRESS: {i+1}/{total} - Processing: {item}", flush=True)

        # Simulate processing work
        await asyncio.sleep(0.5)  # Simulate processing time

        # Process the item (convert to uppercase and add metadata)
        processed_item = {
            "original": item,
            "processed": item.upper(),
            "timestamp": time.strftime('%H:%M:%S'),
            "index": i,
            "based_on_phrase": phrase
        }
        results.append(processed_item)

        # Log completion of this item
        progress_log.append(f"[{time.strftime('%H:%M:%S')}] ✅ Completed: {item} → {item.upper()}")

    # Report final completion using FastMCP Context
    if ctx:
        try:
            await ctx.report_progress(progress=total, total=total)
            await ctx.info(f"🎉 All {total} items processed successfully")
            progress_log.append(f"[{time.strftime('%H:%M:%S')}] ✅ FastMCP Context: All {total} items completed")
        except Exception as e:
            progress_log.append(f"[{time.strftime('%H:%M:%S')}] ⚠️ Context error: {e}")
            print(f"🔄 FALLBACK: Processing complete: {total}/{total} items", flush=True)
    else:
        progress_log.append(f"[{time.strftime('%H:%M:%S')}] 🎉 Processing complete: {total}/{total} items")
        print(f"🔄 PROGRESS: Processing complete: {total}/{total} items", flush=True)

    # Calculate final metrics
    end_time = time.time()
    total_duration = end_time - start_time

    # Create result data structure (similar to echostring_table format)
    result_data = {
        "type": "processing_results",
        "title": f"Processing Results for: {phrase}",
        "summary": {
            "base_phrase": phrase,
            "total_items": total,
            "processed_count": len(results),
            "duration_seconds": round(total_duration, 2),
            "items_per_second": round(len(results) / total_duration, 2)
        },
        "progress_log": progress_log,
        "results": results,
        "timestamp": time.strftime('%H:%M:%S')
    }

    return json.dumps(result_data, indent=2)


async def echostring_longrunning(phrase: str = Field(description="Phrase to echo during a long-running task")) -> str:
    """Echo a string while simulating a long-running task with multiple stages

    This function simulates a 60-second long-running task with multiple stages,
    providing progress updates and intermediate outputs. Useful for testing
    how the MCP system handles long-running operations.

    Args:
        phrase: the phrase to echo during the long-running task

    Returns:
        A detailed log of the long-running task with timestamps and progress
    """
    start_time = time.time()
    output_lines = []

    # Stage 1: Initialization (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Starting long-running task with phrase: '{phrase}'")
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 1/6: Initializing...")
    await asyncio.sleep(5)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Setting up environment for '{phrase}'")
    await asyncio.sleep(5)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Initialization complete")

    # Stage 2: Data Processing (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 2/6: Processing data...")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Analyzing '{phrase}' (25% complete)")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Processing variations of '{phrase}' (50% complete)")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Data processing complete")

    # Stage 3: Computation (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 3/6: Running computations...")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Computing echo patterns for '{phrase}'")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Optimizing output format")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Computation complete")

    # Stage 4: Validation (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 4/6: Validating results...")
    await asyncio.sleep(2)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Checking integrity of '{phrase}' processing")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Running quality assurance tests")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Validation complete")

    # Stage 5: Finalization (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 5/6: Finalizing output...")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Formatting final result for '{phrase}'")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Preparing delivery")
    await asyncio.sleep(3)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Finalization complete")

    # Stage 6: Completion (10 seconds)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage 6/6: Completing task...")
    await asyncio.sleep(2)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Generating final echo: '{phrase}, {phrase}, {phrase}'")
    await asyncio.sleep(4)
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Cleaning up resources")
    await asyncio.sleep(4)

    # Final summary
    end_time = time.time()
    duration = end_time - start_time
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Task completed successfully!")
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Total duration: {duration:.1f} seconds")
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Final result: {phrase}, {phrase}, {phrase} - long-running task complete!")

    return "\n".join(output_lines)


async def echostring_streaming_progress(phrase: str = Field(description="Phrase to echo with simulated streaming progress")) -> str:
    """Echo a string while simulating streaming progress updates

    This function demonstrates how to structure a long-running task that could
    potentially support streaming if the underlying FastMCP version supports it.
    Currently returns all output at completion but shows the pattern for streaming.

    Args:
        phrase: the phrase to echo during the long-running task

    Returns:
        A detailed log of the long-running task with timestamps and progress
    """
    start_time = time.time()
    output_lines = []

    def log_progress(message_type: str, message: str, **kwargs):
        """Log progress in a structured format (ready for streaming when available)"""
        timestamp = time.strftime('%H:%M:%S')

        # Create structured progress data (like what ctx.emit() would send)
        progress_data = {
            "type": message_type,
            "message": message,
            "timestamp": timestamp,
            **kwargs
        }

        # Format for current output
        formatted_message = f"[{timestamp}] {message}"
        output_lines.append(formatted_message)

        # Log to server console (visible in real-time)
        print(f"🔄 PROGRESS: {formatted_message}", flush=True)

        # In a streaming-enabled version, this would be:
        # await ctx.emit(progress_data)

        return formatted_message

    # Initial progress
    log_progress("progress", f"🎯 Starting streaming task with phrase: '{phrase}'",
                 stage=0, total_stages=6)
    log_progress("info", "📋 Will execute 6 stages, ~10 seconds each",
                 estimated_duration=60)

    # Execute 6 stages with progress updates
    for stage in range(1, 7):
        stage_names = [
            "Initialization", "Data Processing", "Computation",
            "Validation", "Finalization", "Completion"
        ]
        stage_name = stage_names[stage - 1]
        stage_start = time.time()

        # Stage start
        log_progress("stage_start", f"🚀 Stage {stage}/6: {stage_name}",
                    stage=stage, stage_name=stage_name, phrase=phrase)

        # Stage-specific work with intermediate updates
        if stage == 1:  # Initialization
            await asyncio.sleep(5)
            log_progress("progress", f"- Setting up environment for '{phrase}'", stage=stage)
            await asyncio.sleep(5)
            log_progress("progress", "- Environment ready", stage=stage)

        elif stage == 2:  # Data Processing
            await asyncio.sleep(3)
            log_progress("progress", f"- Analyzing '{phrase}' (33% complete)",
                        stage=stage, progress_percent=33)
            await asyncio.sleep(4)
            log_progress("progress", "- Processing variations (66% complete)",
                        stage=stage, progress_percent=66)
            await asyncio.sleep(3)
            log_progress("progress", "- Data analysis complete",
                        stage=stage, progress_percent=100)

        elif stage == 3:  # Computation
            await asyncio.sleep(4)
            log_progress("progress", f"- Computing echo patterns for '{phrase}'", stage=stage)
            await asyncio.sleep(3)
            log_progress("progress", "- Optimizing output format", stage=stage)
            await asyncio.sleep(3)

        elif stage == 4:  # Validation
            await asyncio.sleep(2)
            log_progress("progress", f"- Validating '{phrase}' processing", stage=stage)
            await asyncio.sleep(4)
            log_progress("progress", "- Running quality assurance tests", stage=stage)
            await asyncio.sleep(4)

        elif stage == 5:  # Finalization
            await asyncio.sleep(3)
            log_progress("progress", f"- Formatting final result for '{phrase}'", stage=stage)
            await asyncio.sleep(4)
            log_progress("progress", "- Preparing delivery", stage=stage)
            await asyncio.sleep(3)

        elif stage == 6:  # Completion
            await asyncio.sleep(2)
            log_progress("progress", f"- Generating final echo: '{phrase}, {phrase}, {phrase}'", stage=stage)
            await asyncio.sleep(4)
            log_progress("progress", "- Cleaning up resources", stage=stage)
            await asyncio.sleep(4)

        # Stage completion
        stage_duration = time.time() - stage_start
        log_progress("stage_complete", f"✅ Stage {stage}/6 Complete: {stage_name}",
                    stage=stage, stage_name=stage_name, duration=round(stage_duration, 1),
                    overall_progress=round((stage / 6) * 100))

    # Final completion
    total_duration = time.time() - start_time
    log_progress("completion", "🎉 ALL STAGES COMPLETE!",
                total_duration=round(total_duration, 1),
                final_result=f"{phrase}, {phrase}, {phrase}")

    return "\n".join(output_lines)


async def echostring_quick_stage(
    phrase: str = Field(description="Phrase to process"),
    stage: int = Field(description="Stage number (1-6, or 0 to run all stages automatically)", default=0),
    duration: int = Field(description="Duration per stage in seconds (default 10)", default=10)
) -> str:
    """Execute one stage or all stages of a multi-stage long-running task

    This tool can either run a single stage (for true incremental progress) or
    all stages at once. For incremental progress, call with stage=1, then stage=2, etc.
    For convenience, stage=0 runs all stages automatically (but shows output only at the end).

    Args:
        phrase: The phrase to process
        stage: Stage number (1-6 for individual stages, 0 for all stages)
        duration: How long each stage should take (default 10 seconds)

    Returns:
        Status and results for the completed stage(s)
    """
    stage_names = [
        "Initialization",
        "Data Processing",
        "Computation",
        "Validation",
        "Finalization",
        "Completion"
    ]

    # If stage=0, run all stages (original behavior)
    if stage == 0:
        all_output_lines = []
        overall_start_time = time.time()

        all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] 🎯 Starting Multi-Stage Task with phrase: '{phrase}'")
        all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] 📋 Will execute 6 stages, {duration} seconds each")
        all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] ⏱️  Estimated total time: {duration * 6} seconds")
        all_output_lines.append("")

        # Execute all 6 stages
        for current_stage in range(1, 7):
            stage_name = stage_names[current_stage - 1]
            stage_start_time = time.time()

            # Stage header
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] 🚀 Starting Stage {current_stage}/6: {stage_name}")
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] Processing phrase: '{phrase}'")

            # Stage-specific processing
            await _execute_stage_work(current_stage, phrase, duration, all_output_lines)

            # Stage completion
            stage_end_time = time.time()
            stage_duration = stage_end_time - stage_start_time

            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] ✅ Stage {current_stage}/6 Complete: {stage_name}")
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] Stage duration: {stage_duration:.1f} seconds")

            # Progress indicator
            progress_percent = (current_stage / 6) * 100
            all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] 📊 Overall progress: {progress_percent:.0f}% ({current_stage}/6 stages)")
            all_output_lines.append("")  # Empty line for readability

        # Final summary
        overall_end_time = time.time()
        total_duration = overall_end_time - overall_start_time

        all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] 🎉 ALL STAGES COMPLETE!")
        all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] Total duration: {total_duration:.1f} seconds")
        all_output_lines.append(f"[{time.strftime('%H:%M:%S')}] Final result: {phrase}, {phrase}, {phrase} - multi-stage task complete!")

        return "\n".join(all_output_lines)

    # Single stage execution (for true incremental progress)
    if stage < 1 or stage > 6:
        return f"❌ Invalid stage number: {stage}. Must be between 1 and 6 (or 0 for all stages)."

    stage_name = stage_names[stage - 1]
    start_time = time.time()

    # Stage-specific processing
    output_lines = []
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] 🚀 Starting Stage {stage}/6: {stage_name}")
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Processing phrase: '{phrase}'")

    # Execute the stage work
    await _execute_stage_work(stage, phrase, duration, output_lines)

    end_time = time.time()
    actual_duration = end_time - start_time

    output_lines.append(f"[{time.strftime('%H:%M:%S')}] ✅ Stage {stage}/6 Complete: {stage_name}")
    output_lines.append(f"[{time.strftime('%H:%M:%S')}] Duration: {actual_duration:.1f} seconds")

    if stage == 6:
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] 🎉 ALL STAGES COMPLETE!")
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] Final result: {phrase}, {phrase}, {phrase}")
    else:
        next_stage = stage + 1
        next_name = stage_names[next_stage - 1]
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] ➡️  Next: Stage {next_stage}/6 ({next_name})")
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] 💡 Call: echostring_quick_stage('{phrase}', {next_stage})")

    return "\n".join(output_lines)


async def _execute_stage_work(stage: int, phrase: str, duration: int, output_lines: list):
    """Helper function to execute the work for a specific stage"""
    if stage == 1:  # Initialization
        await asyncio.sleep(duration // 2)
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Setting up environment for '{phrase}'")
        await asyncio.sleep(duration // 2)
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Environment ready")

    elif stage == 2:  # Data Processing
        await asyncio.sleep(duration // 3)
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Analyzing '{phrase}' (33% complete)")
        await asyncio.sleep(duration // 3)
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Processing variations (66% complete)")
        await asyncio.sleep(duration // 3)
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Data analysis complete")

    elif stage == 3:  # Computation
        await asyncio.sleep(duration // 2)
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Computing echo patterns for '{phrase}'")
        await asyncio.sleep(duration // 2)
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Optimization complete")

    elif stage == 4:  # Validation
        await asyncio.sleep(duration // 2)
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Validating '{phrase}' processing")
        await asyncio.sleep(duration // 2)
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Quality checks passed")

    elif stage == 5:  # Finalization
        await asyncio.sleep(duration // 2)
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Formatting results for '{phrase}'")
        await asyncio.sleep(duration // 2)
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Output prepared")

    elif stage == 6:  # Completion
        await asyncio.sleep(duration // 2)
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Generating final echo: '{phrase}, {phrase}, {phrase}'")
        await asyncio.sleep(duration // 2)
        output_lines.append(f"[{time.strftime('%H:%M:%S')}] - Task cleanup complete")


async def get_company_categories_matching(query: str = Field(description="The domain of the company to search for")) -> str:
    """Get a listing of companies matching the search criteria.

    Args:
        query: The domain of the company to search for
    """
    return "Consider only the most closely related categories. " + json.dumps(org_frame_cols)


async def get_llm_completion(sort_criteria: str = Field(description="The criteria for sorting")) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.
    Common sort_criteria:
    total_funding_usd_mm
    num_funding_rounds
    last_funding_on
    founded_on
    """
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        cols=['uuid', sort_criteria],
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    uuids = co.uuid.head(2).to_list()

    uuids_str = [f"'{u}'" for u in uuids]
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=f"uuid in ({','.join(uuids_str)})",
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    print(co.head(2))
    return co.head(2).to_string()


async def get_top_companies(sort_criteria: str = Field(description="The criteria for sorting")) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.
    Common sort_criteria:
    total_funding_usd_mm
    num_funding_rounds
    last_funding_on
    founded_on
    """
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        cols=['uuid', sort_criteria],
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    uuids = co.uuid.head(2).to_list()

    uuids_str = [f"'{u}'" for u in uuids]
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=f"uuid in ({','.join(uuids_str)})",
    )
    co = co.sort_values(by=sort_criteria, ascending=False)
    print(co.head(2))
    return co.head(2).to_string()


async def get_company_listing(
    domain: str = Field(description="The domain of the company to search for", default=None),
    name: str = Field(description="The name of the company to search for", default=None)
) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.

    Args:
        domain: The domain of the company to search for
        name: The name of the company to search for
    """
    where_clauses = []
    if domain:
        where_clauses.append(f"domain='{domain}'")
    if name:
        where_clauses.append(f"name like '{name}%'")

    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=' and '.join(where_clauses)
    )
    print(co.head())
    return co.to_string()
