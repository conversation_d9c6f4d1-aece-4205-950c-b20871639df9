#!/usr/bin/env python3
"""
MCP HTTP Debug Client

This is a debug client for testing the MCP server via streamable HTTP.
It provides detailed information about the server connection and tool calls.
"""

import asyncio
import sys
import argparse
import json
import time
from typing import Any, Dict, List, Optional

from mcp_http_clientlib import MCPClientLib, ToolCallResult

# --- Configuration ---
DEFAULT_SERVER_URL = "http://0.0.0.0:9000/streamablehttp"  # Default URL for HTTP

def print_json(data: Any):
    """Print data as formatted JSON."""
    print(json.dumps(data, indent=2, default=str))

def debug_callback(level: str, message: str, data: Any = None):
    """Debug callback that prints detailed information."""
    print(f"[{level.upper()}] {message}")
    if data is not None:
        if isinstance(data, (dict, list)):
            print_json(data)
        else:
            print(f"  Data: {data}")

async def main():
    """Main entry point."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="MCP HTTP Debug Client")
    parser.add_argument("--server", "-s", default=DEFAULT_SERVER_URL,
                        help=f"Server URL (default: {DEFAULT_SERVER_URL})")
    parser.add_argument("--tool", "-t", default="echostring",
                        help="Tool to call (default: echostring)")
    parser.add_argument("--input", "-i", default="Hello, world!",
                        help="Input for the tool (default: 'Hello, world!')")
    args = parser.parse_args()

    # Create client with debug callback
    client = MCPClientLib(debug_callback=debug_callback)

    try:
        # Connect to server
        print(f"Connecting to MCP server via streamable HTTP at: {args.server}")
        start_time = time.time()
        success = await client.connect_to_server(args.server)
        connect_time = time.time() - start_time

        if not success:
            print("Failed to connect to server. Exiting.")
            return

        print(f"Connected in {connect_time:.2f} seconds.")

        # Print available tools
        print("\nAvailable Tools:")
        for i, tool in enumerate(client.available_tools, 1):
            print(f"{i}. {tool['name']}: {tool['description']}")
            print(f"   Input Schema: {json.dumps(tool['input_schema'], indent=2)}")

        # Call the specified tool
        print(f"\nCalling tool: '{args.tool}' with input: '{args.input}'")
        start_time = time.time()
        result = await client.call_tool(args.tool, args.input)
        call_time = time.time() - start_time

        # Print the result
        print(f"\nTool call completed in {call_time:.2f} seconds.")
        print(f"Success: {result.success}")
        if result.success:
            print(f"Result: {result.content}")
        else:
            print(f"Error: {result.error}")

        # Print full result details
        print("\nFull Result Details:")
        print_json(result.to_dict())

    finally:
        # Ensure cleanup runs
        await client.cleanup()
        print("Cleanup complete.")

if __name__ == "__main__":
    asyncio.run(main())
