#!/usr/bin/env python3
"""
MCP HTTP Streamlit App

This is a Streamlit app for interacting with MCP servers via streamable HTTP.
It provides a web interface for calling tools and processing queries with <PERSON>.
"""

import streamlit as st
import asyncio
import os
import sys
import json
from typing import Dict, List, Any, Optional

# Add the parent directory to the path so we can import the client library
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from proto_mcp_http.mcp_http_clientlib import <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolCallResult

# --- Configuration ---
DEFAULT_SERVER_URL = "http://0.0.0.0:9000/streamable_http"  # Default URL for HTTP
DEFAULT_MODEL = "claude-3-5-sonnet-20240620"  # Default Claude model

# Initialize session state
if "messages" not in st.session_state:
    st.session_state.messages = []
if "client" not in st.session_state:
    st.session_state.client = None
if "connected" not in st.session_state:
    st.session_state.connected = False
if "available_tools" not in st.session_state:
    st.session_state.available_tools = []
if "debug_messages" not in st.session_state:
    st.session_state.debug_messages = []
if "tool_results" not in st.session_state:
    st.session_state.tool_results = []

# Set up the page
st.set_page_config(page_title="MCP HTTP Client", page_icon="🤖", layout="wide")
st.title("MCP HTTP Client")

# Sidebar for configuration
with st.sidebar:
    st.header("Configuration")
    
    # Server URL
    server_url = st.text_input("Server URL", DEFAULT_SERVER_URL)
    
    # Anthropic API key
    api_key = st.text_input("Anthropic API Key", os.environ.get("ANTHROPIC_API_KEY", ""), type="password")
    
    # Claude model
    model = st.selectbox("Claude Model", [
        "claude-3-5-sonnet-20240620",
        "claude-3-opus-20240229",
        "claude-3-sonnet-20240229",
        "claude-3-haiku-20240307"
    ])
    
    # Debug mode
    debug_mode = st.checkbox("Debug Mode", False)
    
    # Connect button
    if st.button("Connect to Server"):
        with st.spinner("Connecting to server..."):
            # Define debug callback
            def debug_callback(level, message, data=None):
                st.session_state.debug_messages.append({
                    "level": level,
                    "message": message,
                    "data": data
                })
                return None  # Not a coroutine
            
            # Create client
            client = MCPClientLib(
                anthropic_api_key=api_key if api_key else None,
                debug_callback=debug_callback if debug_mode else None
            )
            
            # Connect to server
            try:
                # Run the async function using asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                success = loop.run_until_complete(client.connect_to_server(server_url))
                
                if success:
                    st.session_state.client = client
                    st.session_state.connected = True
                    st.session_state.available_tools = client.available_tools
                    st.success("Connected to server successfully!")
                else:
                    st.error("Failed to connect to server.")
            except Exception as e:
                st.error(f"Error connecting to server: {e}")
    
    # Disconnect button
    if st.session_state.connected and st.button("Disconnect"):
        with st.spinner("Disconnecting..."):
            try:
                # Run the async function using asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(st.session_state.client.cleanup())
                
                st.session_state.client = None
                st.session_state.connected = False
                st.session_state.available_tools = []
                st.success("Disconnected from server.")
            except Exception as e:
                st.error(f"Error disconnecting: {e}")
    
    # Clear conversation button
    if st.button("Clear Conversation"):
        st.session_state.messages = []
        st.session_state.tool_results = []
        st.success("Conversation cleared.")
    
    # Show available tools
    if st.session_state.connected:
        st.header("Available Tools")
        for tool in st.session_state.available_tools:
            with st.expander(tool["name"]):
                st.write(tool["description"])
                st.json(tool["input_schema"])

# Main chat interface
col1, col2 = st.columns([2, 1])

with col1:
    # Display conversation
    st.header("Conversation")
    
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.write(message["content"])
    
    # Chat input
    if st.session_state.connected:
        prompt = st.chat_input("Type your message here...")
        if prompt:
            # Add user message to conversation
            st.session_state.messages.append({"role": "user", "content": prompt})
            
            # Display user message
            with st.chat_message("user"):
                st.write(prompt)
            
            # Process the query
            with st.chat_message("assistant"):
                with st.spinner("Thinking..."):
                    try:
                        # Run the async function using asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        result = loop.run_until_complete(
                            st.session_state.client.process_query(
                                query=prompt,
                                model=model,
                                max_tokens=1024,
                                tool_timeout=30,  # 30 second timeout for tool calls
                                conversation_history=st.session_state.messages[:-1]  # Exclude the message we just added
                            )
                        )
                        
                        # Store tool results
                        if result["tool_results"]:
                            st.session_state.tool_results.extend(result["tool_results"])
                        
                        # Update conversation history
                        st.session_state.messages = result["messages"]
                        
                        # Display the response
                        if result["error"]:
                            st.error(f"Error: {result['error']}")
                        else:
                            st.write(result["final_text"])
                    except Exception as e:
                        st.error(f"Error processing query: {e}")
    else:
        st.info("Connect to a server to start chatting.")

with col2:
    # Tool results tab
    tab1, tab2 = st.tabs(["Tool Results", "Debug Messages"])
    
    with tab1:
        st.header("Tool Results")
        if st.session_state.tool_results:
            for i, result in enumerate(st.session_state.tool_results):
                with st.expander(f"Tool Call {i+1}: {result.tool_name}"):
                    st.write(f"**Input:** `{result.tool_input}`")
                    st.write(f"**Success:** {result.success}")
                    if result.success:
                        st.write(f"**Result:**")
                        st.code(result.content)
                    else:
                        st.error(f"**Error:** {result.error}")
                    st.write(f"**Execution Time:** {result.execution_time:.2f}s")
        else:
            st.info("No tool calls yet.")
    
    with tab2:
        st.header("Debug Messages")
        if debug_mode:
            if st.session_state.debug_messages:
                for msg in st.session_state.debug_messages:
                    with st.expander(f"[{msg['level'].upper()}] {msg['message'][:50]}..."):
                        st.write(f"**Level:** {msg['level'].upper()}")
                        st.write(f"**Message:** {msg['message']}")
                        if msg['data'] is not None:
                            st.write("**Data:**")
                            try:
                                st.json(msg['data'])
                            except:
                                st.code(str(msg['data']))
            else:
                st.info("No debug messages yet.")
        else:
            st.info("Enable debug mode to see debug messages.")
