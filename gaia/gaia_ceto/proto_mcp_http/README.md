# MCP HTTP Client and Server

This directory contains implementations of MCP (Model-Call-Protocol) clients and servers using the streamable HTTP protocol.

## Overview

The MCP HTTP implementation provides the same functionality as the SSE version but uses the streamable HTTP protocol instead. This can be useful in environments where SSE is not well-supported or when you need more control over the HTTP connection.

## Components

### Server

- `mcp_http_server.py`: An MCP server implementation using the streamable HTTP protocol. It provides tools like `echostring`, `get_company_listing`, etc.

### Client Libraries

- `mcp_http_clientlib.py`: A reusable client library for interacting with MCP servers via streamable HTTP. It handles connecting to the server, calling tools, and processing queries with <PERSON>.

### Client Applications

- `mcp_http_client_light.py`: A lightweight command-line client for interacting with MCP servers.
- `mcp_direct_echostring.py`: A simple client that directly calls the echostring tool without using Claude.
- `mcp_http_debug.py`: A debug client for testing the MCP server with detailed output.
- `mcp_http_streamlit.py`: A Streamlit web app for interacting with MCP servers.

## Usage

### Starting the Server

```bash
python mcp_http_server.py --port 9000
```

This will start the MCP server on port 9000 using the streamable HTTP protocol.

### Using the Command-Line Client

```bash
python mcp_http_client_light.py --server http://0.0.0.0:9000/streamablehttp
```

This will connect to the MCP server and allow you to interact with it via a command-line interface.

### Using the Direct Echostring Client

```bash
python mcp_direct_echostring.py --server http://0.0.0.0:9000/streamablehttp "Hello, world!"
```

This will directly call the echostring tool with the provided phrase.

### Using the Debug Client

```bash
python mcp_http_debug.py --server http://0.0.0.0:9000/streamablehttp --tool echostring --input "Hello, world!"
```

This will provide detailed information about the server connection and tool call.

### Using the Streamlit App

```bash
streamlit run mcp_http_streamlit.py
```

This will start a Streamlit web app for interacting with the MCP server.

## Differences from SSE Version

The main difference between the HTTP and SSE versions is the transport protocol:

- The SSE version uses Server-Sent Events (SSE) for communication.
- The HTTP version uses streamable HTTP for communication.

The functionality is otherwise identical, with the same tools and capabilities.

## Environment Variables

- `ANTHROPIC_API_KEY`: Your Anthropic API key for using Claude.

## Dependencies

- `mcp`: The Model-Call-Protocol library.
- `anthropic`: The Anthropic Python client for using Claude.
- `streamlit`: For the web app (only needed for `mcp_http_streamlit.py`).
- `uvicorn`: For running the server.
- `fastapi`: Used by the MCP server.

## Notes

- The server and clients are designed to be compatible with each other.
- The clients can be used with any MCP server that supports the streamable HTTP protocol.
- The server can be used with any MCP client that supports the streamable HTTP protocol.
