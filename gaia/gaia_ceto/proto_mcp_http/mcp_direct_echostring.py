#!/usr/bin/env python3
"""
MCP Direct Echostring Client

This is a simple client that directly calls the echostring tool on an MCP server
via streamable HTTP without using <PERSON>.
"""

import asyncio
import sys
import argparse
from mcp_http_clientlib import MC<PERSON>lientLib

# --- Configuration ---
DEFAULT_SERVER_URL = "http://0.0.0.0:9000/mcp"  # Default URL for HTTP

async def main():
    """Main entry point."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="MCP Direct Echostring Client")
    parser.add_argument("--server", "-s", default=DEFAULT_SERVER_URL,
                        help=f"Server URL (default: {DEFAULT_SERVER_URL})")
    parser.add_argument("phrase", nargs="?", default="Hello, world!",
                        help="Phrase to echo (default: 'Hello, world!')")
    args = parser.parse_args()

    # Create client
    client = MCPClientLib(debug_callback=lambda level, message, data: print(f"[{level.upper()}] {message}"))

    try:
        # Connect to server
        print(f"Connecting to MCP server via streamable HTTP at: {args.server}")
        success = await client.connect_to_server(args.server)

        if not success:
            print("Failed to connect to server. Exiting.")
            return

        # Call the echostring tool directly
        print(f"Calling echostring tool with phrase: '{args.phrase}'")
        result = await client.call_tool("echostring", args.phrase)

        # Print the result
        if result.success:
            print(f"\nResult: {result.content}")
        else:
            print(f"\nError: {result.error}")

    finally:
        # Ensure cleanup runs
        await client.cleanup()
        print("Cleanup complete.")

if __name__ == "__main__":
    asyncio.run(main())
