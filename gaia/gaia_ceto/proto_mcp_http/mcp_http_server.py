#!/usr/bin/env python3
"""
MCP HTTP Server

This module implements an MCP server using the streamable HTTP protocol.
It provides the same functionality as the SSE version but uses HTTP streaming.
"""

from mcp.server.fastmcp import FastMCP
import gaia
from pydantic import Field
from gaia.gaia_frames import gaia_frames
import pandas as pd
import json
import argparse
import uvicorn

# Initialize FastMCP server
mcp = FastMCP("gaia_mcp_http_server")

org_frame_cols={}

@mcp.tool()
async def echostring(phrase: str=Field(description="Phrase to echo", )) -> str:
    """Echo a string

    Args:
        phrase: the phrase to echo
    """
    # label_cols is a dict of label_name -> list of column names
    # we want to return this as single string in JSON format
    return phrase + ", " + phrase + ", " + phrase + " you weasly wabbit... "


@mcp.tool()
async def get_company_categories_matching(query: str=Field(description="The domain of the company to search for", )) -> str:
    """Get a listing of companies matching the search criteria.

    Args:
        name: The name of the company to search for
    """
    # label_cols is a dict of label_name -> list of column names
    # we want to return this as single string in JSON format
    return "Consider only the most closely related categories. " + json.dumps(org_frame_cols)


@mcp.tool()
async def get_llm_completion(sort_criteria: str=Field(description="The criteria for sorting"),) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.
    Common sort_criteria:
    total_funding_usd_mm
    num_funding_rounds
    last_funding_on
    founded_on
    """
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        cols=['uuid',sort_criteria], 
    )   
    co = co.sort_values(by=sort_criteria, ascending=False)
    uuids = co.uuid.head(2).to_list()

    uuids_str = [f"'{u}'" for u in uuids]
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=f"uuid in ({','.join( uuids_str )})" , )
    co = co.sort_values(by=sort_criteria, ascending=False)
    print(co.head(2))
    return co.head(2).to_string()


@mcp.tool()
async def get_top_companies(sort_criteria: str=Field(description="The criteria for sorting"),) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.
    Common sort_criteria:
    total_funding_usd_mm
    num_funding_rounds
    last_funding_on
    founded_on
    """
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        cols=['uuid',sort_criteria], 
    )   
    co = co.sort_values(by=sort_criteria, ascending=False)
    uuids = co.uuid.head(2).to_list()

    uuids_str = [f"'{u}'" for u in uuids]
    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=f"uuid in ({','.join( uuids_str )})" , )
    co = co.sort_values(by=sort_criteria, ascending=False)
    print(co.head(2))
    return co.head(2).to_string()


@mcp.tool()
async def get_company_listing(domain: str=Field(description="The domain of the company to search for", default=None), name: str=Field(description="The name of the company to search for", default=None),) -> str:
    """Get a single or small set of matching companies for the (name/domain) search criteria.

    Args:
        name: The name of the company to search for
    """
    where_clauses= []
    if domain:
        where_clauses.append(f"domain='{domain}'")
    if name:
        where_clauses.append(f"name like '{name}%'")

    co = gaia_frames.read_frame_pandas(
        section_slug='cb',
        frame_slug="organizations",
        where=' and '.join(where_clauses)
    )
    print(co.head())
    return co.to_string()


if __name__ == "__main__":
    # Initialize and run the server
    parser = argparse.ArgumentParser()
    parser.add_argument("--port", type=int, default=9000, help="HTTP port")
    args = parser.parse_args()

    # Create the app using streamable_http_app instead of sse_app
    app = mcp.streamable_http_app()

    uvicorn.run(app, host="0.0.0.0", port=args.port, log_level="info")
