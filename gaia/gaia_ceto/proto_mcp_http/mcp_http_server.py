#!/usr/bin/env python3
"""
MCP HTTP Server

This module implements an MCP server using the streamable HTTP protocol.
It provides the same functionality as the SSE version but uses HTTP streaming.
"""

from mcp.server.fastmcp import FastMCP
from fastmcp import Context
from pydantic import Field
import argparse
import uvicorn

# Import shared tools from the common module
from gaia.gaia_ceto.proto_mcp.mcp_tools import (
    echostring,
    echostring_table,
    echostring_process_items,
    echostring_longrunning,
    echostring_streaming_progress,
    echostring_quick_stage,
    get_company_categories_matching,
    get_llm_completion,
    get_top_companies,
    get_company_listing,
    org_frame_cols
)

# Initialize FastMCP server
mcp = FastMCP("gaia_mcp_http_server")

# Test: Define a Context-enabled tool directly in the server file
@mcp.tool()
async def test_context_in_server(
    phrase: str = Field(description="Test phrase"),
    ctx: Context | None = None
) -> str:
    """Test Context injection when tool is defined directly in server file"""
    if ctx:
        try:
            await ctx.info(f"✅ SERVER-DEFINED TOOL: Context works! Phrase: {phrase}")
            await ctx.report_progress(1, 1)
            return f"✅ SUCCESS: Server-defined tool got Context! Phrase: {phrase}"
        except Exception as e:
            return f"⚠️ PARTIAL: Context received but error: {e}"
    else:
        return f"❌ FAILED: Server-defined tool got no Context. Phrase: {phrase}"

# Diagnostic tool to check FastMCP environment
@mcp.tool()
def diagnose_fastmcp_environment() -> str:
    """Diagnose FastMCP environment and Context support"""
    import fastmcp
    import inspect

    results = []
    results.append("🔍 FastMCP Environment Diagnosis:")
    results.append(f"FastMCP version: {getattr(fastmcp, '__version__', 'unknown')}")
    results.append(f"FastMCP location: {fastmcp.__file__}")
    results.append(f"Context class: {Context}")
    results.append(f"Context location: {Context.__module__}")

    # Check if Context has expected methods
    context_methods = [attr for attr in dir(Context) if not attr.startswith('_')]
    results.append(f"Context methods: {context_methods}")

    # Check server configuration
    results.append(f"Server name: {mcp.name}")
    results.append(f"Server type: {type(mcp)}")

    # Check tool registration method
    sig = inspect.signature(test_context_in_server)
    results.append(f"Test tool signature: {sig}")

    return "\n".join(results)

# Register the shared tools with the MCP server using add_tool method
mcp.add_tool(echostring)
mcp.add_tool(echostring_table)
mcp.add_tool(echostring_process_items)
mcp.add_tool(echostring_longrunning)
mcp.add_tool(echostring_streaming_progress)
mcp.add_tool(echostring_quick_stage)
mcp.add_tool(get_company_categories_matching)
mcp.add_tool(get_llm_completion)
mcp.add_tool(get_top_companies)
mcp.add_tool(get_company_listing)


if __name__ == "__main__":
    # Initialize and run the server
    parser = argparse.ArgumentParser()
    parser.add_argument("--port", type=int, default=9000, help="HTTP port")
    args = parser.parse_args()

    # Create the app using streamable_http_app for HTTP streaming
    app = mcp.streamable_http_app()

    uvicorn.run(app, host="0.0.0.0", port=args.port, log_level="info")
