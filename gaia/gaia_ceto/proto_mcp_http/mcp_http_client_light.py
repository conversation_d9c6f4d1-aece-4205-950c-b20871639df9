#!/usr/bin/env python3
"""
MCP HTTP Client (Lightweight Version)

This is a lightweight command-line client for interacting with MCP servers via streamable HTTP.
It uses the MCPClientLib for core functionality.
"""

import asyncio
import sys
import argparse
import os
from typing import Optional, List, Dict, Any

# Import our client library
from mcp_http_clientlib import MC<PERSON><PERSON><PERSON><PERSON>, ToolCallResult

# --- Configuration ---
DEFAULT_SERVER_URL = "http://0.0.0.0:9000/mcp"  # Default URL for HTTP
DEFAULT_MODEL = "claude-3-5-sonnet-20240620"  # Default Claude model

class CommandLineClient:
    """Command-line interface for the MCP HTTP client."""

    def __init__(self, server_url: str, anthropic_api_key: Optional[str] = None, debug: bool = False):
        """Initialize the command-line client.

        Args:
            server_url: The HTTP URL of the server's streamable HTTP endpoint.
            anthropic_api_key: Optional API key for Anthropic.
            debug: Whether to enable debug output.
        """
        self.server_url = server_url
        self.anthropic_api_key = anthropic_api_key
        self.debug = debug
        self.client = None
        self.messages = []

    def debug_callback(self, level: str, message: str, data: Any = None):
        """Callback for debug messages from the client library."""
        if self.debug or level in ["error", "warning"]:
            print(f"[{level.upper()}] {message}")
            if data and self.debug:
                print(f"  Data: {data}")

    async def run(self):
        """Run the command-line client."""
        try:
            # Create the client with our debug callback
            self.client = MCPClientLib(
                anthropic_api_key=self.anthropic_api_key,
                debug_callback=self.debug_callback
            )

            # Connect to the server
            print(f"Connecting to MCP server via streamable HTTP at: {self.server_url}")
            success = await self.client.connect_to_server(self.server_url)

            if not success:
                print("Failed to connect to server. Exiting.")
                return

            # Print available tools
            tool_names = [tool['name'] for tool in self.client.available_tools]
            print(f"Connected to server with tools: {', '.join(tool_names)}")

            # Simple chat loop
            print("\nMCP Client (Lightweight Version)")
            print("Type your queries or 'quit' to exit.")
            print("Type 'debug on' or 'debug off' to toggle debug output.")
            print("Type 'clear' to clear the conversation history.")

            while True:
                query = input("\nQuery: ").strip()

                if not query:
                    continue

                if query.lower() == 'quit':
                    print("Exiting chat loop.")
                    break

                if query.lower() == 'debug on':
                    self.debug = True
                    print("Debug output enabled.")
                    continue

                if query.lower() == 'debug off':
                    self.debug = False
                    print("Debug output disabled.")
                    continue

                if query.lower() == 'clear':
                    self.messages = []
                    print("Conversation history cleared.")
                    continue

                # Process the query
                print("\nProcessing query...")
                result = await self.client.process_query(
                    query=query,
                    model=DEFAULT_MODEL,
                    max_tokens=1024,
                    tool_timeout=70,  # 70 second timeout for long-running tools
                    conversation_history=self.messages
                )

                # Update conversation history
                self.messages = result["messages"]

                # Print any errors
                if result["error"]:
                    print(f"\nError: {result['error']}")
                else:
                    # Print tool results if any
                    if result["tool_results"]:
                        print(f"\nClaude requested {len(result['tool_results'])} tool call(s).")
                        for i, tool_result in enumerate(result["tool_results"]):
                            print(f"\nTool Call {i+1}: '{tool_result.tool_name}'")
                            if tool_result.success:
                                print(f"Result: {tool_result.content}")
                            else:
                                print(f"Error: {tool_result.error}")

                    # Print Claude's response
                    print("\nClaude Response:")
                    print(result["final_text"])

        except KeyboardInterrupt:
            print("\nExiting...")
        except Exception as e:
            print(f"\nFatal error: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # Ensure cleanup runs
            if self.client:
                await self.client.cleanup()
                print("Cleanup complete.")

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="MCP HTTP Client (Lightweight Version)")
    parser.add_argument("--server", "-s", default=DEFAULT_SERVER_URL,
                        help=f"Server URL (default: {DEFAULT_SERVER_URL})")
    parser.add_argument("--api-key", "-k", default=None,
                        help="Anthropic API key (default: use environment variable)")
    parser.add_argument("--debug", "-d", action="store_true",
                        help="Enable debug output")
    return parser.parse_args()

async def main():
    """Main entry point."""
    args = parse_args()

    # Get API key from args or environment
    api_key = args.api_key or os.environ.get("ANTHROPIC_API_KEY")

    # Create and run the client
    client = CommandLineClient(
        server_url=args.server,
        anthropic_api_key=api_key,
        debug=args.debug
    )
    await client.run()

if __name__ == "__main__":
    asyncio.run(main())
