<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    {% csrf_token %}
    <!-- Vue.js -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <!-- Axios for HTTP requests -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- Marked for Markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        :root {
            --primary-color: #35424a;
            --secondary-color: #4a90e2;
            --background-color: #f4f4f4;
            --text-color: #333;
            --light-text: #666;
            --border-color: #ddd;
            --success-color: #4caf50;
            --error-color: #f44336;
            --warning-color: #ff9800;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        .app-container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 300px;
            background-color: var(--primary-color);
            color: white;
            padding: 1rem;
            overflow-y: auto;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-container {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background-color: white;
        }
        
        .input-container {
            padding: 1rem;
            background-color: #f9f9f9;
            border-top: 1px solid var(--border-color);
        }
        
        .button {
            padding: 0.5rem 1rem;
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            margin-top: 0.5rem;
        }
        
        .input-box {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            resize: none;
            font-family: inherit;
            font-size: 1rem;
        }
        
        .alert {
            padding: 0.5rem;
            margin-bottom: 1rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        
        .alert-error {
            background-color: #ffebee;
            border-left: 4px solid var(--error-color);
        }
        
        .empty-state {
            text-align: center;
            padding: 2rem;
            color: var(--light-text);
        }
    </style>
</head>
<body>
    <div id="app" class="app-container">
        <div class="sidebar">
            <h2>Gaia Chat</h2>
            
            <!-- User Info -->
            <div class="user-info">
                <p>Welcome, {{ user.username }}</p>
                <div class="user-links">
                    <a href="{% url 'gaia_chat:profile' %}" class="user-link">Profile</a>
                    <a href="{% url 'gaia_chat:logout' %}" class="user-link">Logout</a>
                </div>
            </div>
            
            <div class="alert alert-error">
                <p>This is a minimal version of the chat app. Please copy all template files to the production server.</p>
            </div>
        </div>
        
        <div class="main-content">
            <div class="chat-container">
                <div class="empty-state">
                    <h3>Template Error Fixed</h3>
                    <p>The template file has been found, but you need to copy the complete template files to the production server.</p>
                    <p>Please follow the instructions provided in the fix_templates.sh script.</p>
                </div>
            </div>
            
            <div class="input-container">
                <textarea 
                    class="input-box" 
                    placeholder="Template files need to be copied to the production server" 
                    disabled
                    rows="3"
                ></textarea>
                <button class="button" disabled>
                    Send
                </button>
            </div>
        </div>
    </div>
</body>
</html>
